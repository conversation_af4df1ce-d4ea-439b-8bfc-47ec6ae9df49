import 'dart:convert';
import 'dart:developer';

import 'package:db_eats/bloc/account_bloc.dart';
import 'package:db_eats/common/initializer.dart';
import 'package:db_eats/storage/localstorage.dart';
import 'package:db_eats/ui/home.dart';
import 'package:db_eats/ui/mainnavigationpage.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:geocoding/geocoding.dart';
import 'package:geolocator/geolocator.dart';
import 'package:http/http.dart' as http;

// Google Maps API key
const String kGoogleApiKey = "AIzaSyCpAdQaZ3fPe5H0wfkI0NqMXcT8J7AW9uY";

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  final TextEditingController _addressController = TextEditingController();
  final TextEditingController _zipCodeController = TextEditingController();
  bool _isLoading = false;
  String? _selectedPlaceId;
  String? _selectedAddress;
  double? _selectedLatitude;
  double? _selectedLongitude;
  List<Prediction> _searchResults = [];
  bool _showPredictions = false;

  @override
  void initState() {
    super.initState();
    _checkSavedAddress();
  }

  Future<void> _checkSavedAddress() async {
    await Future.delayed(const Duration(seconds: 2));

    final token = await LocalStorage.getAccessToken();

    // Only auto-navigate if user is authenticated
    if (token != null && token.isNotEmpty && mounted) {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(builder: (context) => const MainNavigationScreen()),
      );
    }
    // If not authenticated, do nothing and wait for user input
  }

  Future<void> _handleDeliverHere() async {
    setState(() => _isLoading = true);

    try {
      final token = await LocalStorage.getAccessToken();

      if (token != null && token.isNotEmpty) {
        // If token exists, directly navigate to MainNavigationScreen
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const MainNavigationScreen()),
        );
      } else {
        // Only validate address if no token exists
        if (_addressController.text.trim().isEmpty) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Please enter or select an address')),
          );
          return;
        }

        if (_selectedAddress == null) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Please select a valid address')),
          );
          return;
        }

        await Initializer.saveAddress(_selectedAddress!);
        if (_selectedLatitude != null && _selectedLongitude != null) {
          final initializer = Initializer();
          initializer.setCoordinates(_selectedLatitude!, _selectedLongitude!);
        }

        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const Home()),
        );
      }
    } catch (e) {
      log('Error handling delivery: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: $e')),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _getCurrentLocation() async {
    setState(() => _isLoading = true);

    try {
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Location permissions are denied')),
          );
          setState(() => _isLoading = false);
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
              content: Text(
                  'Location permissions are permanently denied, please enable in settings')),
        );
        setState(() => _isLoading = false);
        return;
      }

      Position position = await Geolocator.getCurrentPosition();

      double lat = position.latitude;
      double lon = position.longitude;
      Initializer.latitude = lat.toString();
      Initializer.longitude = lon.toString();
      log('Latitude: ${Initializer.latitude}, Longitude: ${Initializer.longitude}');

      List<Placemark> placemarks = await placemarkFromCoordinates(lat, lon);

      if (placemarks.isNotEmpty) {
        Placemark place = placemarks[0];
        String address = '';

        if (place.street != null && place.street!.isNotEmpty) {
          address += place.street!;
        }

        if (place.locality != null && place.locality!.isNotEmpty) {
          if (address.isNotEmpty) address += ', ';
          address += place.locality!;
        }

        if (place.administrativeArea != null &&
            place.administrativeArea!.isNotEmpty) {
          if (address.isNotEmpty) address += ', ';
          address += place.administrativeArea!;
        }

        setState(() {
          _selectedAddress = address;
          _selectedLatitude = lat;
          _selectedLongitude = lon;
          _addressController.text = address;
          _zipCodeController.text = place.postalCode ?? '';
          _showPredictions = false;
          _searchResults = [];
          _isLoading = false;
        });
      } else {
        setState(() => _isLoading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
              content: Text('No address found for current location')),
        );
      }
    } catch (e) {
      log('Error getting location: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error getting location: $e')),
      );
      setState(() => _isLoading = false);
    }
  }

  Future<void> searchPlaces(String query) async {
    if (query.isEmpty) {
      setState(() {
        _showPredictions = false;
        _searchResults = [];
      });
      return;
    }

    try {
      final response = await http.get(
        Uri.parse('https://maps.googleapis.com/maps/api/place/autocomplete/json'
            '?input=$query'
            '&types=address'
            '&key=$kGoogleApiKey'),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['status'] == 'OK') {
          final results = (data['predictions'] as List)
              .map((p) => Prediction(
                    p['description'],
                    p['place_id'],
                    p['structured_formatting']?['main_text'],
                  ))
              .toList();

          setState(() {
            _searchResults = results;
            _showPredictions = _searchResults.isNotEmpty;
          });
        } else {
          throw Exception('Places API error: ${data['status']}');
        }
      } else {
        throw Exception('Failed to fetch places: ${response.statusCode}');
      }
    } catch (e) {
      log('Error searching places: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error searching places: $e')),
      );
    }
  }

  Future<void> selectPlace(Prediction prediction) async {
    setState(() => _isLoading = true);

    try {
      final response = await http.get(
        Uri.parse('https://maps.googleapis.com/maps/api/place/details/json'
            '?place_id=${prediction.placeId}'
            '&fields=formatted_address,geometry'
            '&key=$kGoogleApiKey'),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['status'] == 'OK') {
          final location = data['result']['geometry']['location'];
          final lat = location['lat'];
          final lng = location['lng'];
          final address = data['result']['formatted_address'];

          setState(() {
            _selectedAddress = address;
            _selectedLatitude = lat;
            _selectedLongitude = lng;
            _addressController.text = address;
            _showPredictions = false;
            _searchResults = [];
            _selectedPlaceId = prediction.placeId;
            _isLoading = false;
          });
        } else {
          throw Exception('Place details API error: ${data['status']}');
        }
      } else {
        throw Exception(
            'Failed to fetch place details: ${response.statusCode}');
      }
    } catch (e) {
      log('Error getting place details: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error getting place details: $e')),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  late double ten;
  late double twelve;
  late double forteen;
  late double sixteen;
  late double eighteen;
  late double twenty;
  late double twentyFour;
  late double screenWidth;
  late double screenHeight;
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    screenWidth = MediaQuery.of(context).size.width;
    screenHeight = MediaQuery.of(context).size.height;

    ten = screenWidth * 0.02545;
    twelve = screenWidth * 0.03054;
    forteen = screenWidth * 0.035;
    sixteen = screenWidth * 0.04073;
    eighteen = screenWidth * 0.04582;
    twenty = screenWidth * 0.05091;
    twentyFour = screenWidth * 0.06109;
  }

  double _getResponsiveFontSize(BuildContext context, double size) {
    double screenWidth = MediaQuery.of(context).size.width;
    double baseWidth = 375.0;
    double scaleFactor = screenWidth / baseWidth;
    double scaledSize = size * scaleFactor;
    return scaledSize.clamp(size * 0.8, size * 1.2);
  }

  @override
  void dispose() {
    _addressController.dispose();
    _zipCodeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidth = MediaQuery.of(context).size.width;

    return BlocListener<AccountBloc, AccountState>(
      listener: (context, state) {
        if (state is AddCurrentAddressSuccess) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Address saved successfully')),
          );
        } else if (state is AddCurrentAddressFailed) {
          setState(() => _isLoading = false);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Failed to save address: ${state.message}')),
          );
        }
      },
      child: Scaffold(
        resizeToAvoidBottomInset: true,
        body: Stack(
          children: [
            Positioned.fill(
              child: Image.asset(
                'assets/images/splashscreen_bg.png',
                fit: BoxFit.cover,
                width: double.infinity,
                height: double.infinity,
                // Add alignment to ensure consistent positioning
                alignment: Alignment.center,
              ),
            ),
            Positioned(
              top: MediaQuery.of(context).padding.top - screenHeight * 0.005,
              left: 16,
              child: SafeArea(
                child: Image.asset(
                  'assets/logow.png',
                  width: (screenHeight * 0.005) * 37.22,
                  height: (screenHeight * 0.005) * 10,
                  fit: BoxFit.contain,
                ),
              ),
            ),
            SafeArea(
              child: SingleChildScrollView(
                child: ConstrainedBox(
                  constraints: BoxConstraints(
                    minHeight: MediaQuery.of(context).size.height -
                        MediaQuery.of(context).padding.top -
                        MediaQuery.of(context).padding.bottom,
                  ),
                  child: Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: MediaQuery.of(context).size.width * 0.06,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        SizedBox(
                          height: MediaQuery.of(context).size.height * 0.44,
                          // width: double.infinity,
                        ),
                        Text(
                          'Home-Cooked\nComfort,\nWherever You Are',
                          style: TextStyle(
                              color: Colors.white,
                              //   fontSize: _getResponsiveFontSize(context, 44),
                              fontSize: ten * 4.3,
                              fontWeight: FontWeight.w400,
                              fontFamily: "Inter",
                              height: 1.1,
                              letterSpacing: -1),
                        ),
                        SizedBox(
                            height: MediaQuery.of(context).size.height * 0.02),
                        Text(
                          'Enjoy fresh, preservative-free dishes\nprepared by local home chefs delivered\nright to your door. Experience the taste\nof home on the go.',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: _getResponsiveFontSize(context, 18),
                            fontWeight: FontWeight.w400,
                            height: 1.4,
                          ),
                        ),
                        SizedBox(
                            height: MediaQuery.of(context).size.height * 0.054),
                        FutureBuilder<List<dynamic>>(
                          future: Future.wait([
                            LocalStorage.getAccessToken(),
                            Initializer.getSavedAddress(),
                          ]),
                          builder: (context, snapshot) {
                            if (!snapshot.hasData) {
                              return Container(); // Loading state
                            }

                            final token = snapshot.data![0] as String?;
                            final savedAddress = snapshot.data![1] as String?;

                            // Hide inputs if user is authenticated
                            if (token != null && token.isNotEmpty) {
                              return Container();
                            }

                            // Show inputs and button when user is not authenticated
                            return Column(
                              children: [
                                _buildAddressInput(),
                                SizedBox(
                                    height: MediaQuery.of(context).size.height *
                                        0.018),
                                _buildDeliverButton(),
                              ],
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAddressInput() {
    final screenSize = MediaQuery.of(context).size;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (_showPredictions && _searchResults.isNotEmpty)
          Container(
            margin: EdgeInsets.only(bottom: screenSize.height * 0.01),
            constraints: BoxConstraints(maxHeight: screenSize.height * 0.25),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: ListView.separated(
                padding: EdgeInsets.zero,
                shrinkWrap: true,
                itemCount: _searchResults.length,
                separatorBuilder: (context, index) => const Divider(height: 1),
                itemBuilder: (context, index) {
                  final prediction = _searchResults[index];
                  return ListTile(
                    dense: true,
                    title: Text(
                      prediction.description ?? '',
                      style: TextStyle(
                        fontSize: _getResponsiveFontSize(context, 14),
                      ),
                    ),
                    onTap: () => selectPlace(prediction),
                  );
                },
              ),
            ),
          ),
        Container(
          height: screenSize.height * 0.05,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(25),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                child: Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: screenSize.width * 0.05,
                  ),
                  child: Center(
                    child: TextField(
                      controller: _addressController,
                      maxLines: 1,
                      textAlign: TextAlign.center,
                      decoration: InputDecoration(
                        hintText: 'Enter Address or Zip code',
                        border: InputBorder.none,
                        hintStyle: TextStyle(
                          color: Colors.grey,
                          fontWeight: FontWeight.w400,
                          fontSize: _getResponsiveFontSize(context, 16),
                          overflow: TextOverflow.ellipsis,
                        ),
                        isCollapsed: true,
                        contentPadding: EdgeInsets.zero,
                      ),
                      style: TextStyle(
                        fontWeight: FontWeight.w400,
                        fontSize: _getResponsiveFontSize(context, 16),
                        overflow: TextOverflow.ellipsis,
                      ),
                      onChanged: searchPlaces,
                    ),
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsets.only(right: screenSize.width * 0.04),
                child: GestureDetector(
                  onTap: _getCurrentLocation,
                  child: Row(
                    children: [
                      SizedBox(
                        width: 24,
                        height: 24,
                        child: _isLoading
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                      Colors.black54),
                                ),
                              )
                            : Image.asset(
                                'assets/icons/location2.png',
                                width: _getResponsiveFontSize(context, 20),
                                height: _getResponsiveFontSize(context, 20),
                                fit: BoxFit.contain,
                              ),
                      ),
                      SizedBox(width: screenSize.width * 0.02),
                    IntrinsicWidth(
  child: Column(
    mainAxisSize: MainAxisSize.min,
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Text(
        'Locate me',
        style: TextStyle(
          fontSize: _getResponsiveFontSize(context, 14),
          fontWeight: FontWeight.w400,
          decoration: TextDecoration.none,
        ),
      ),
      SizedBox(height: 0),
      Container(
        height: 1,
        width: double.infinity, // This will now match the text width
        color: Colors.black,
      ),
    ],
  ),
)
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDeliverButton() {
    final screenSize = MediaQuery.of(context).size;

    return SizedBox(
      width: double.infinity,
      height: screenSize.height * 0.05,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _handleDeliverHere,
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFFFFBE16),
          foregroundColor: Colors.black,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(25),
          ),
          elevation: 0,
        ),
        child: _isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.black),
                ),
              )
            : Text(
                'Deliver Here',
                style: TextStyle(
                  fontSize: _getResponsiveFontSize(context, 16),
                  // fontWeight: FontWeight.w400,
                  fontFamily: 'Inter-medium',
                ),
              ),
      ),
    );
  }
}

class Prediction {
  final String? description;
  final String? placeId;
  final String? mainText;

  Prediction(this.description, this.placeId, this.mainText);
}
