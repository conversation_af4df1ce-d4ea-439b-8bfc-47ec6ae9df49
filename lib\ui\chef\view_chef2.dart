import 'dart:async';
import 'dart:convert';
import 'dart:developer';

import 'package:db_eats/bloc/account_bloc.dart';
import 'package:db_eats/bloc/home_bloc.dart';
import 'package:db_eats/bloc/mealplan_bloc.dart';
import 'package:db_eats/common/initializer.dart';
import 'package:db_eats/data/models/chef/viewchefdetailsmodel.dart';
import 'package:db_eats/data/models/chef/viewdishesmodel.dart';
import 'package:db_eats/data/models/guesthome/listaddressmodel.dart';
import 'package:db_eats/data/models/meal_plan/timinglistmodel.dart'
    as meal_plan;
import 'package:db_eats/server/serverhelper.dart';
import 'package:db_eats/ui/cart/yourcart.dart';
import 'package:db_eats/ui/catering/dishdetail.dart';
import 'package:db_eats/ui/filter_dish_model.dart';
import 'package:db_eats/ui/mainnavigationpage.dart';
import 'package:db_eats/widgets/floatingactionbutton.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:geocoding/geocoding.dart';
import 'package:geolocator/geolocator.dart';
import 'package:http/http.dart' as http;
import 'package:shimmer/shimmer.dart';

class ViewChef2 extends StatefulWidget {
  final int id;
  final String title;
  final double latitude;
  final double longitude;
  final String? distance;

  const ViewChef2({
    super.key,
    required this.id,
    required this.title,
    required this.latitude,
    required this.longitude,
    this.distance,
  });

  @override
  State<ViewChef2> createState() => _ViewChef2State();
}

class _ViewChef2State extends State<ViewChef2>
    with SingleTickerProviderStateMixin {
  ChefDetailsModel? chefDetails;
  DishesListModel? dishesData;
  late TabController _tabController;
  List<String> _categories = [];

  // Location header state variables
  late final MealplanBloc _mealplanBloc;
  bool _isSearchPopupVisible = false;
  OverlayEntry? _overlayEntry;
  String _selectedDeliveryTime = 'ASAP';
  String _currentlySelectedTimeRange = '7:00 AM - 7:30 AM';
  bool _showLocationMenu = false;
  bool _showTimeOptions = false;
  double? _currentLatitude;
  double? _currentLongitude;
  String? _currentAddress;
  List<meal_plan.Timings>? _availableTimings;
  List<AddressData>? _savedAddresses;
  AddressData? _currentAddressData;
  bool _loadingAddresses = false;
  bool _isSearching = false;
  List<Prediction> _searchResults = [];
  bool _showPredictions = false;
  final TextEditingController _addressController = TextEditingController();
  final String kGoogleApiKey = "AIzaSyCpAdQaZ3fPe5H0wfkI0NqMXcT8J7AW9uY";
  double? _pendingLatitude;
  double? _pendingLongitude;
  int? _selectedTimeId;
  Timer? _debounce;

  @override
  void initState() {
    super.initState();
    _mealplanBloc = MealplanBloc();
    _loadAddresses();
    _mealplanBloc.add(ListTimingEvent());
    _mealplanBloc.add(GetAddedTimePreferences());

    // Set initial delivery time based on Initializer data
    if (Initializer.addedTimePreferenceModel.data != null) {
      final timeData = Initializer.addedTimePreferenceModel.data!;
      _selectedDeliveryTime = timeData.isDeliverNow == true
          ? 'ASAP'
          : timeData.timePreference != null
              ? '${_formatTimeToAMPM(timeData.timePreference!.startTime)} - ${_formatTimeToAMPM(timeData.timePreference!.endTime)}'
              : 'ASAP';
    }

    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<HomeBloc>().add(ViewChefDetailsEvent(chefId: widget.id));
      context.read<HomeBloc>().add(GetDishesListEvent(
            data: {
              "chef_id": widget.id.toString(),
              "search_keyword": "",
              "packaging_type_id": "",
              "cuisine_ids": "",
              "sub_cuisine_ids": "",
              "local_cuisine_ids": "",
              "dietary_preference_id": "",
              "spice_level_id": ""
            },
          ));

      // Initialize latitude and longitude from widget parameters
      // _currentLatitude = widget.latitude;
      // _currentLongitude = widget.longitude;
    });
  }

  late double ten;
  late double twelve;
  late double forteen;
  late double sixteen;
  late double eighteen;
  late double twenty;
  late double twentyFour;
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final screenWidth = MediaQuery.of(context).size.width;

    ten = screenWidth * 0.02545;
    twelve = screenWidth * 0.03054;
    forteen = screenWidth * 0.035;
    sixteen = screenWidth * 0.04073;
    eighteen = screenWidth * 0.04582;
    twenty = screenWidth * 0.05091;
    twentyFour = screenWidth * 0.06109;
  }

  Future<void> _loadAddresses() async {
    setState(() => _loadingAddresses = true);
    try {
      context.read<AccountBloc>().add(ListAddressesEvent());
    } catch (e) {
      log('Error loading addresses: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to load addresses: $e')),
      );
    } finally {
      setState(() => _loadingAddresses = false);
    }
  }

  void _selectAddress(AddressData address) async {
    try {
      context.read<AccountBloc>().add(
            EditAddressEvent({
              "id": address.id ?? 0,
              "is_current": true,
            }),
          );
      _pendingLatitude = address.location?.coordinates?[1];
      _pendingLongitude = address.location?.coordinates?[0];
    } catch (e) {
      log('Error selecting address: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to select address: $e')),
      );
    }
  }

  int _selectedIndex = 0;

  final List<Widget> _pages = [
    const Center(child: Text('Home Page')),
    const Center(child: Text('Orders Page')),
    const Center(child: Text('Catering Page')),
    const Center(child: Text('Messages Page')),
    const Center(child: Text('Account Page')),
  ];

  void _onNavItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  Future<void> _navigateBack() async {
    final savedFilters = await Initializer.getAppliedFilters();
    if (savedFilters != null) {
      // If we have filters, merge them with coordinates
      final requestData = <String, dynamic>{
        ...savedFilters,
        'latitude': Initializer.latitude,
        'longitude': Initializer.longitude,
      };
      context.read<HomeBloc>().add(GetHomeDataEvent(data: requestData));
    }
    Navigator.pop(context);
  }

  void _openCart() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const CartPage(),
      ),
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    _addressController.dispose();
    _closeSearchPopup();
    _mealplanBloc.close();
    _debounce?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    return MultiBlocProvider(
      providers: [
        BlocProvider<MealplanBloc>.value(value: _mealplanBloc),
      ],
      child: MultiBlocListener(
        listeners: [
          BlocListener<HomeBloc, HomeState>(
            listener: (context, state) {
              if (state is ChefDetailsSuccess) {
                setState(() {
                  chefDetails = state.data;
                });
              }
              if (state is DishesListSuccess) {
                setState(() {
                  dishesData = state.data;
                  _categories = dishesData?.data?.categoryBasedList
                          ?.map((cat) => cat.category?.name ?? '')
                          .toList() ??
                      [];
                  _tabController =
                      TabController(length: _categories.length, vsync: this);
                });
              }
            },
          ),
          BlocListener<AccountBloc, AccountState>(
            listener: (context, state) {
              if (state is AddFavouriteChefSuccess) {
                ScaffoldMessenger.of(context)
                  ..hideCurrentSnackBar()
                  ..showSnackBar(SnackBar(content: Text(state.message)));
              } else if (state is AddFavouriteChefFailed) {
                ScaffoldMessenger.of(context)
                  ..hideCurrentSnackBar()
                  ..showSnackBar(
                    SnackBar(
                        content: Text(state.message),
                        backgroundColor: Colors.red),
                  );
              }
              if (state is ListAddressesSuccess) {
                setState(() {
                  _savedAddresses = state.data;
                  _currentAddressData = _savedAddresses?.firstWhere(
                    (address) => address.isCurrent == true,
                    orElse: () => _savedAddresses?.isNotEmpty == true
                        ? _savedAddresses!.first
                        : AddressData(),
                  );
                });
                if (_currentAddressData?.location?.coordinates != null) {
                  final lat = _currentAddressData!.location!.coordinates![1];
                  final lng = _currentAddressData!.location!.coordinates![0];
                  setState(() {
                    _currentLatitude = lat;
                    _currentLongitude = lng;
                  });
                  Initializer().setCoordinates(lat, lng);
                  context.read<HomeBloc>().add(
                        GetHomeDataEvent(
                          data: {
                            'latitude': lat,
                            'longitude': lng,
                          },
                        ),
                      );
                }
              } else if (state is EditAddressSuccess) {
                if (_pendingLatitude != null && _pendingLongitude != null) {
                  setState(() {
                    _currentLatitude = _pendingLatitude;
                    _currentLongitude = _pendingLongitude;
                    _showLocationMenu = false;
                    _pendingLatitude = null;
                    _pendingLongitude = null;
                  });
                  Initializer()
                      .setCoordinates(_currentLatitude!, _currentLongitude!);
                  context.read<HomeBloc>().add(
                        GetHomeDataEvent(
                          data: {
                            'latitude': _currentLatitude!,
                            'longitude': _currentLongitude!,
                          },
                        ),
                      );
                }
                _loadAddresses();
              } else if (state is AddAddressSuccess) {
                context.read<AccountBloc>().add(ListAddressesEvent());
              }
            },
          ),
          BlocListener<MealplanBloc, MealPlanState>(
            listener: (context, state) {
              if (state is ListTimingSuccess) {
                setState(() {
                  _availableTimings = state.data.data?.timings;
                });
              } else if (state is GettingAddedTimePreferencesSuccess) {
                if (state.data != null) {
                  setState(() {
                    _selectedDeliveryTime = state.data.isDeliverNow == true
                        ? 'ASAP'
                        : state.data.timePreference != null
                            ? '${_formatTimeToAMPM(state.data.timePreference!.startTime)} - ${_formatTimeToAMPM(state.data.timePreference!.endTime)}'
                            : 'ASAP';
                  });
                }
              } else if (state is AddTimePreferencesSuccess) {
                // Fetch updated time preferences after successfully adding them
                _mealplanBloc.add(GetAddedTimePreferences());
                setState(() {
                  _showTimeOptions = false;
                  _showLocationMenu = false;
                });
              }
            },
          ),
        ],
        child: WithNavBar(
          currentIndex: 0,
          child: WillPopScope(
            onWillPop: () async {
              _navigateBack();
              return false;
            },
            child: Scaffold(
              backgroundColor: const Color(0xFFF6F3EC),
              floatingActionButton: CartFloatingActionButton(
                itemCount: Initializer.cartCount ?? 0,
                onPressed: _openCart,
              ),
              body: SafeArea(
                child: Stack(
                  children: [
                    Column(
                      children: [
                        _buildLocationHeader(
                            context, screenWidth, screenHeight),
                        Expanded(
                          child: BlocBuilder<HomeBloc, HomeState>(
                            builder: (context, state) {
                              if (state is DishesListLoading ||
                                  state is LoadingChefDetails) {
                                return _buildShimmerLoading(
                                    context, screenWidth, screenHeight);
                              }
                              return SingleChildScrollView(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    // Chef profile section
                                    Padding(
                                      padding: EdgeInsets.symmetric(
                                        horizontal: screenWidth * 0.035,
                                        vertical: screenHeight * 0.01,
                                      ),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          SizedBox(
                                            height: screenHeight * 0.18,
                                            child: Stack(
                                              clipBehavior: Clip.none,
                                              children: [
                                                Container(
                                                  width: double.infinity,
                                                  height: screenHeight * 0.148,
                                                  decoration: BoxDecoration(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            screenWidth * 0.04),
                                                    image: DecorationImage(
                                                      image: NetworkImage(
                                                        ServerHelper.imageUrl +
                                                            (chefDetails
                                                                    ?.data
                                                                    ?.chef
                                                                    ?.coverPhoto ??
                                                                ''),
                                                      ),
                                                      fit: BoxFit.cover,
                                                      onError: (exception,
                                                              stackTrace) =>
                                                          const AssetImage(
                                                              'assets/images/placeholder.png'),
                                                    ),
                                                  ),
                                                ),
                                                Align(
                                                  alignment:
                                                      Alignment.bottomRight,
                                                  child: FractionalTranslation(
                                                    translation: const Offset(
                                                        -0.21, -0.03),
                                                    child: Container(
                                                      decoration: BoxDecoration(
                                                        shape: BoxShape.circle,
                                                        border: Border.all(
                                                          color: Colors.white,
                                                          width: screenWidth *
                                                              0.015,
                                                        ),
                                                      ),
                                                      child: CircleAvatar(
                                                        radius:
                                                            screenWidth * 0.1,
                                                        backgroundImage:
                                                            NetworkImage(
                                                          ServerHelper
                                                                  .imageUrl +
                                                              (chefDetails
                                                                      ?.data
                                                                      ?.chef
                                                                      ?.profilePhoto ??
                                                                  ''),
                                                        ),
                                                        backgroundColor:
                                                            Colors.white,
                                                        onBackgroundImageError:
                                                            (exception,
                                                                stackTrace) {},
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                          Row(
                                            children: [
                                              Expanded(
                                                child: Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    Text(
                                                      '${chefDetails?.data?.chef?.chef?.firstName ?? ''} ${chefDetails?.data?.chef?.chef?.lastName ?? ''}',
                                                      style: TextStyle(
                                                        fontFamily: 'Inter',
                                                        fontSize: eighteen,
                                                        fontWeight:
                                                            FontWeight.w600,
                                                        height: 1.24,
                                                        letterSpacing: -1,
                                                        color: const Color(
                                                            0xFF1F2122),
                                                      ),
                                                      maxLines: 1,
                                                      overflow:
                                                          TextOverflow.ellipsis,
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ],
                                          ),
                                          SizedBox(
                                              height: screenHeight * 0.017),
                                          // Row(
                                          //   children: [
                                          //     // _buildMetricContainer(
                                          //     //   context,
                                          //     //   screenWidth,
                                          //     //   icon: Icons.access_time,
                                          //     //   text: "35 mins",
                                          //     // ),
                                          //     // SizedBox(
                                          //     //     width: screenWidth * 0.02),
                                          //     _buildMetricContainer(
                                          //       context,
                                          //       screenWidth,
                                          //       icon: Icons.star,
                                          //       text: chefDetails?.data?.chef
                                          //               ?.averageRating ??
                                          //           '0.0',
                                          //     ),
                                          //     SizedBox(
                                          //         width: screenWidth * 0.02),
                                          //     _buildMetricContainer(
                                          //       context,
                                          //       screenWidth,
                                          //       icon:
                                          //           Icons.location_on_outlined,
                                          //       text: widget.distance ?? '_',
                                          //     ),
                                          //     SizedBox(
                                          //         width: screenWidth * 0.018),
                                          //     _buildFavoriteButton(
                                          //         context, screenWidth),
                                          //   ],
                                          // ),

                                          Row(
                                            children: [
                                              Container(
                                                padding:  EdgeInsets.symmetric(
                                                    horizontal: twelve/2, vertical: 0),
                                                constraints:  BoxConstraints(
                                                    minWidth: ten*5, minHeight: ten*3.4),
                                                decoration: BoxDecoration(
                                                  color: const Color(0xFFF6F3EC),
                                                  borderRadius: BorderRadius.circular(ten),
                                                  border:
                                                      Border.all(color: const Color(0xFFB9B6AD)),
                                                ),
                                                child: Row(
                                                  children:  [
                                                    Icon(Icons.access_time,
                                                        size: sixteen, color: Color(0xFF414346)),
                                                    SizedBox(width: sixteen/4),
                                                    Text(
                                                      "35 mins",
                                                      style: TextStyle(
                                                        fontFamily: 'Inter',
                                                        fontSize: forteen,
                                                        fontWeight: FontWeight.w600,
                                                        height: 24 / 16,
                                                        color: Color(0xFF1F2122),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                               ),
                                              SizedBox(width: sixteen / 2),
                                              Container(
                                                padding: EdgeInsets.symmetric(
                                                    horizontal: twelve / 2,
                                                    vertical: 0),
                                                constraints: BoxConstraints(
                                                    minWidth: ten * 6.1,
                                                    minHeight: ten * 3.4),
                                                decoration: BoxDecoration(
                                                  color:
                                                      const Color(0xFFF6F3EC),
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                          ten),
                                                  border: Border.all(
                                                      color: const Color(
                                                          0xFFB9B6AD)),
                                                ),
                                                child: Row(
                                                  children: [
                                                    Icon(Icons.star,
                                                        size: sixteen,
                                                        color:
                                                            Color(0xFF414346)),
                                                    SizedBox(
                                                        width: sixteen / 4),
                                                    Text(
                                                      "4.9",
                                                      style: TextStyle(
                                                        fontFamily: 'Inter',
                                                        fontSize: forteen,
                                                        fontWeight:
                                                            FontWeight.w500,
                                                        height: 24 / 16,
                                                        color:
                                                            Color(0xFF1F2122),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              SizedBox(width: sixteen / 2),
                                              Container(
                                                padding: EdgeInsets.symmetric(
                                                    horizontal: twelve / 2,
                                                    vertical: 0),
                                                constraints: BoxConstraints(
                                                    minWidth: ten * 8.7,
                                                    minHeight: ten * 3.4),
                                                decoration: BoxDecoration(
                                                  color:
                                                      const Color(0xFFF6F3EC),
                                                  borderRadius:
                                                      BorderRadius.circular(10),
                                                  border: Border.all(
                                                      color: const Color(
                                                          0xFFB9B6AD)),
                                                ),
                                                child: Row(
                                                  children: [
                                                    Icon(
                                                        Icons
                                                            .location_on_outlined,
                                                        size: ten * 1.7,
                                                        color:
                                                            Color(0xFF414346)),
                                                    SizedBox(
                                                        width: sixteen / 4),
                                                    Text(
                                                      widget.distance!,
                                                      style: TextStyle(
                                                        fontFamily: 'Inter',
                                                        fontSize: forteen,
                                                        fontWeight:
                                                            FontWeight.w500,
                                                        height: 24 / 16,
                                                        color:
                                                            Color(0xFF1F2122),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              SizedBox(
                                                  width: screenWidth * 0.018),
                                              _buildFavoriteButton(
                                                  context, screenWidth),
                                              // const SizedBox(width: sixteen/2),
                                              // Container(
                                              //   width: 34,
                                              //   height: 34,
                                              //   decoration: BoxDecoration(
                                              //     color: Colors.white,
                                              //     borderRadius: BorderRadius.circular(10),
                                              //     boxShadow: [
                                              //       BoxShadow(
                                              //         color: Colors.black.withOpacity(0.05),
                                              //         blurRadius: sixteen/4,
                                              //         offset: const Offset(0, 2),
                                              //       ),
                                              //     ],
                                              //   ),
                                              //   child: IconButton(
                                              //     icon: Image.asset(
                                              //       'assets/icons/favorites.png',
                                              //       width: twentyFour
                                              //       height: twentyFour
                                              //       color: Color(0xFF1F2122),
                                              //     ),
                                              //     onPressed: () {},
                                              //     padding: EdgeInsets.zero,
                                              //   ),
                                              // ),
                                            ],
                                          ),
                                          SizedBox(
                                              height: screenHeight * 0.016),
                                          Wrap(
                                            spacing: screenWidth * 0.03,
                                            runSpacing: screenHeight * 0.005,
                                            children: (chefDetails?.data?.chef
                                                        ?.searchTags ??
                                                    [])
                                                .map((tag) {
                                              return Text(
                                                tag,
                                                style: TextStyle(
                                                  fontFamily: 'Inter',
                                                  fontSize: forteen,
                                                  fontWeight: FontWeight.w400,
                                                  color:
                                                      const Color(0xFF414346),
                                                ),
                                              );
                                            }).toList(),
                                          ),
                                          SizedBox(height: screenHeight * 0.02),
                                          Text(
                                            chefDetails
                                                    ?.data?.chef?.description ??
                                                '',
                                            style: TextStyle(
                                              fontFamily: 'Inter',
                                              fontSize: forteen,
                                              fontWeight: FontWeight.w400,
                                              color: const Color(0xFF414346),
                                            ),
                                            textAlign: TextAlign.justify,
                                          ),
                                          SizedBox(
                                              height: screenHeight * 0.017),
                                          if (chefDetails?.data?.chef?.chef
                                                      ?.operationDays !=
                                                  null ||
                                              chefDetails?.data?.chef?.chef
                                                      ?.operationTimes !=
                                                  null)
                                            Row(
                                              children: [
                                                Image.asset(
                                                  'assets/icons/calender_3.png',
                                                  width: screenWidth * 0.05,
                                                  height: screenWidth * 0.045,
                                                  color:
                                                      const Color(0xFF414346),
                                                ),
                                                SizedBox(
                                                    width: screenWidth * 0.04),
                                                Text(
                                                  _formatOperationTimes(
                                                      chefDetails
                                                          ?.data
                                                          ?.chef
                                                          ?.chef
                                                          ?.operationTimes),
                                                  style: TextStyle(
                                                    fontFamily: 'Inter',
                                                    fontWeight: FontWeight.w600,
                                                    fontSize: forteen,
                                                  ),
                                                ),
                                                SizedBox(
                                                    width: screenWidth * 0.04),
                                                Text(
                                                  _formatOperationDays(
                                                      chefDetails
                                                          ?.data
                                                          ?.chef
                                                          ?.chef
                                                          ?.operationDays),
                                                  style: TextStyle(
                                                    fontFamily: 'Inter',
                                                    fontWeight: FontWeight.w400,
                                                    fontSize: forteen,
                                                    letterSpacing: 1,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          SizedBox(
                                              height: screenHeight * 0.017),
                                        ],
                                      ),
                                    ),
                                    Padding(
                                      padding: EdgeInsets.symmetric(
                                          horizontal: screenWidth * 0.035),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          SizedBox(
                                            height: screenHeight * 0.04,
                                            child: ListView(
                                              scrollDirection: Axis.horizontal,
                                              children: _categories
                                                  .asMap()
                                                  .entries
                                                  .map((entry) {
                                                final index = entry.key;
                                                final category = entry.value;
                                                final isSelected =
                                                    _tabController.index ==
                                                        index;
                                                return GestureDetector(
                                                  onTap: () {
                                                    setState(() {
                                                      _tabController
                                                          .animateTo(index);
                                                    });
                                                  },
                                                  child: Container(
                                                    margin: EdgeInsets.only(
                                                        right:
                                                            screenWidth * 0.02),
                                                    padding:
                                                        EdgeInsets.symmetric(
                                                      horizontal:
                                                          screenWidth * 0.03,
                                                      vertical:
                                                          screenHeight * 0.005,
                                                    ),
                                                    decoration: BoxDecoration(
                                                      color: isSelected
                                                          ? const Color(
                                                              0xFFB9B6AD)
                                                          : const Color(
                                                              0xFFE1DDD5),
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              screenWidth *
                                                                  0.05),
                                                    ),
                                                    child: Center(
                                                      child: Text(
                                                        category,
                                                        style: TextStyle(
                                                          fontFamily: 'Inter',
                                                          fontSize: forteen,
                                                          fontWeight:
                                                              FontWeight.w500,
                                                          color: const Color(
                                                              0xFF1F2122),
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                );
                                              }).toList(),
                                            ),
                                          ),
                                          SizedBox(
                                              height: screenHeight * 0.033),
                                          Container(
                                            height: screenHeight * 0.05,
                                            decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(
                                                      screenWidth * 0.06),
                                              border: Border.all(
                                                  color:
                                                      const Color(0xFF1F2122)),
                                            ),
                                            child: InkWell(
                                              borderRadius:
                                                  BorderRadius.circular(
                                                      screenWidth * 0.06),
                                              onTap: () {
                                                showModalBottomSheet(
                                                  context: context,
                                                  isScrollControlled:
                                                      true, // Allows the modal to take full height if needed
                                                  backgroundColor: Colors
                                                      .transparent, // For rounded corners to show properly
                                                  builder: (context) =>
                                                      FilterDishModel(
                                                          chefId: widget.id,
                                                          screenWidth:
                                                              screenWidth),
                                                );
                                              },
                                              child: Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.center,
                                                children: [
                                                  Icon(Icons.tune,
                                                      size: twelve,
                                                      color: const Color(
                                                          0xFF1F2122)),
                                                  SizedBox(
                                                      width:
                                                          screenWidth * 0.02),
                                                  Text(
                                                    "View Filters",
                                                    style: TextStyle(
                                                      fontSize: twelve,
                                                      fontWeight:
                                                          FontWeight.w600,
                                                      color: const Color(
                                                          0xFF1F2122),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                          //   SizedBox(height: screenHeight * 0.01),
                                          // GestureDetector(
                                          //   onTap: () {
                                          //     Navigator.push(
                                          //       context,
                                          //       MaterialPageRoute(
                                          //         builder: (_) => WithNavBar(
                                          //           currentIndex: 2,
                                          //           child:
                                          //               SendCateringRequestPage(chefid: widget.id), // Catering tab index
                                          //         ),
                                          //       ),
                                          //     );
                                          //   },
                                          //   child: Container(
                                          //     height: screenHeight * 0.05,
                                          //     width: double.infinity,
                                          //     padding:
                                          //         const EdgeInsets.symmetric(
                                          //             vertical: 6),
                                          //     decoration: BoxDecoration(
                                          //       color: Colors
                                          //           .black, // Add a background color
                                          //       border: Border.all(
                                          //         color:
                                          //             const Color(0xFFAAADB1),
                                          //       ),
                                          //       borderRadius:
                                          //           BorderRadius.circular(30),
                                          //     ),
                                          //     child: Row(
                                          //       mainAxisAlignment:
                                          //           MainAxisAlignment.center,
                                          //       children: [
                                          //         Text(
                                          //           'Send A Catering Request',
                                          //           style: TextStyle(
                                          //             color: Colors.white,
                                          //             fontSize:
                                          //                 screenWidth * 0.03,
                                          //             fontWeight:
                                          //                 FontWeight.w500,
                                          //           ),
                                          //         ),
                                          //         const SizedBox(width: 8),
                                          //         Icon(
                                          //           Icons.arrow_forward,
                                          //           color: Colors.white,
                                          //           size: screenWidth * 0.04,
                                          //         ),
                                          //       ],
                                          //     ),
                                          //   ),
                                          // ),
                                          SizedBox(height: screenHeight * 0.02),
                                          Text(
                                            "Featured Items",
                                            style: TextStyle(
                                              fontFamily: 'Inter',
                                              fontSize: twenty,
                                              fontWeight: FontWeight.w600,
                                              color: const Color(0xFF1F2122),
                                            ),
                                          ),
                                          SizedBox(
                                              height: screenHeight * 0.017),
                                          _buildFeaturedItems(context,
                                              screenWidth, screenHeight),
                                          SizedBox(height: screenHeight * 0.01),
                                          _buildCategoryContent(context,
                                              screenWidth, screenHeight),
                                          SizedBox(height: screenHeight * 0.03),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMetricContainer(BuildContext context, double screenWidth,
      {required IconData icon, required String text}) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: ten / 5, vertical: 0),
      constraints: BoxConstraints(
          minWidth: screenWidth * 0.2, minHeight: twelve + twenty),
      decoration: BoxDecoration(
        color: const Color(0xFFF6F3EC),
        borderRadius: BorderRadius.circular(screenWidth * 0.025),
        border: Border.all(color: const Color(0xFFB9B6AD)),
      ),
      child: Center(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: sixteen, color: const Color(0xFF414346)),
            SizedBox(width: screenWidth * 0.01),
            Text(
              text,
              style: TextStyle(
                fontFamily: 'Inter',
                fontSize: forteen,
                fontWeight: FontWeight.w500,
                color: const Color(0xFF1F2122),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFavoriteButton(BuildContext context, double screenWidth) {
    return Container(
        width: twenty + twelve,
        height: twenty + twelve,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(screenWidth * 0.025),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: IconButton(
          icon: BlocBuilder<AccountBloc, AccountState>(
            builder: (context, state) {
              final isFavorite = state is AddFavouriteChefSuccess
                  ? true
                  : (chefDetails?.data?.isFavourite ?? false);
              if (state is AddFavouriteChefLoading) {
                return SizedBox(
                    width: twenty + twelve,
                    height: twenty + twelve,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: const AlwaysStoppedAnimation<Color>(
                          Color(0xFF1F2122)),
                    ));
              }
              return Image.asset(
                'assets/icons/favorites.png',
                width: twenty,
                height: twenty,
                color: isFavorite ? Colors.red : const Color(0xFF1F2122),
              );
            },
          ),
          onPressed: () {
            context.read<AccountBloc>().add(
                  AddFavouriteChefEvent(chefDetails?.data?.chef?.chefId ?? 0),
                );
          },
          padding: EdgeInsets.zero,
          tooltip: chefDetails?.data?.isFavourite ?? false
              ? 'Remove from favorites'
              : 'Add to favorites',
        ));
  }

  String _formatOperationTimes(List<dynamic>? operationTimes) {
    if (operationTimes == null || operationTimes.isEmpty) return 'Open: N/A';
    var time = operationTimes.first;
    String startTime = time.timing?.startTime ?? 'N/A';
    String endTime = time.timing?.endTime ?? 'N/A';
    String formattedStart = _formatTimeString(startTime);
    String formattedEnd = _formatTimeString(endTime);
    return 'Open $formattedStart-$formattedEnd';
  }

  String _formatTimeString(String timeStr) {
    if (timeStr == 'N/A') return timeStr;
    try {
      List<String> parts = timeStr.split(':');
      if (parts.length < 2) return timeStr;
      int hour = int.parse(parts[0]);
      bool isPM = hour >= 12;
      if (hour > 12) hour -= 12;
      if (hour == 0) hour = 12;
      return '$hour${isPM ? 'PM' : 'AM'}';
    } catch (e) {
      return timeStr;
    }
  }

  String _formatOperationDays(List<dynamic>? operationDays) {
    if (operationDays == null || operationDays.isEmpty) return 'N/A';
    Map<String, String> dayAbbreviations = {
      'mon': 'M',
      'tue': 'T',
      'wed': 'W',
      'thu': 'Th',
      'fri': 'F',
      'sat': 'Sat',
      'sun': 'Sun',
    };
    List<String> formattedDays = operationDays
        .map((day) {
          String? dayName = day.day?.name?.toLowerCase();
          return dayAbbreviations[dayName] ?? '';
        })
        .where((day) => day.isNotEmpty)
        .toList();
    List<String> dayOrder = ['M', 'T', 'W', 'Th', 'F', 'Sat', 'Sun'];
    formattedDays
        .sort((a, b) => dayOrder.indexOf(a).compareTo(dayOrder.indexOf(b)));
    return formattedDays.isEmpty ? 'N/A' : formattedDays.join(', ');
  }

  Widget _buildFeaturedItems(
      BuildContext context, double screenWidth, double screenHeight) {
    if (dishesData?.data?.featuredList == null ||
        dishesData!.data!.featuredList!.isEmpty) {
      return const SizedBox();
    }
    return SizedBox(
      height: ten * 26 + sixteen,
      child: ListView(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.only(right: screenWidth * 0.04),
        children: dishesData!.data!.featuredList!
            .map((dish) => Container(
                  width: ten * 20 + eighteen,
                  margin: EdgeInsets.only(right: screenWidth * 0.04),
                  child: _buildDishCard(context, screenWidth, screenHeight, {
                    'id': dish.id ?? 0,
                    'name': dish.name ?? '',
                    'photo': dish.photo ?? '',
                    'price': dish.servingSizePrices?.first.price ?? '0.00',
                    'serving_size':
                        dish.servingSizePrices?.first.servingSize?.title ?? '',
                    'serving_size_id':
                        dish.servingSizePrices?.first.servingSize?.id ?? 0,
                  }),
                ))
            .toList(),
      ),
    );
  }

  Widget _buildCategorySection(BuildContext context, double screenWidth,
      double screenHeight, String category) {
    final categoryList = dishesData?.data?.categoryBasedList
        ?.firstWhere((cat) => cat.category?.name == category,
            orElse: () => CategoryBasedList())
        .dishList;
    if (categoryList == null || categoryList.isEmpty) return const SizedBox();
    return SizedBox(
      height: ten * 26 + sixteen,
      child: ListView(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.only(right: screenWidth * 0.04),
        children: categoryList
            .map((dish) => Container(
                  width: ten * 20 + eighteen,
                  margin: EdgeInsets.only(right: screenWidth * 0.04),
                  child: _buildDishCard(context, screenWidth, screenHeight, {
                    'id': dish.id ?? 0,
                    'name': dish.name ?? '',
                    'photo': dish.photo ?? '',
                    'price': dish.servingSizePrices?.first.price ?? '0.00',
                    'serving_size':
                        dish.servingSizePrices?.first.servingSize?.title ?? '',
                    'serving_size_id':
                        dish.servingSizePrices?.first.servingSize?.id ?? 0,
                  }),
                ))
            .toList(),
      ),
    );
  }

  Widget _buildCategoryContent(
      BuildContext context, double screenWidth, double screenHeight) {
    if ((dishesData?.data?.featuredList?.isEmpty ?? true) &&
        ((dishesData?.data?.categoryBasedList?.isEmpty ?? true) ||
            (dishesData?.data?.categoryBasedList
                    ?.every((category) => category.dishList?.isEmpty ?? true) ??
                true))) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              'assets/icons/no_data.png',
              width: screenWidth * 0.3,
              height: screenWidth * 0.3,
            ),
            SizedBox(height: screenHeight * 0.02),
            Text(
              'No dishes available',
              style: TextStyle(
                fontSize: screenWidth * 0.045,
                fontWeight: FontWeight.w500,
                color: const Color(0xFF1F2122),
              ),
            ),
          ],
        ),
      );
    }

    if (dishesData?.data?.categoryBasedList == null ||
        dishesData!.data!.categoryBasedList!.isEmpty) {
      return const SizedBox();
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: dishesData!.data!.categoryBasedList!.map((category) {
        final categoryName = category.category?.name ?? '';
        final dishes = category.dishList ?? [];
        if (dishes.isEmpty) return const SizedBox();
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.symmetric(vertical: screenHeight * 0.01),
              child: Text(
                categoryName,
                style: TextStyle(
                  fontFamily: 'Inter',
                  fontSize: twenty,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF1F2122),
                ),
              ),
            ),
            SizedBox(height: screenHeight * 0.01),
            _buildCategorySection(
                context, screenWidth, screenHeight, categoryName),
            SizedBox(height: screenHeight * 0.02),
          ],
        );
      }).toList(),
    );
  }

  Widget _buildShimmerLoading(
      BuildContext context, double screenWidth, double screenHeight) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.all(screenWidth * 0.04),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: double.infinity,
                    height: screenHeight * 0.14,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(screenWidth * 0.04),
                    ),
                  ),
                  SizedBox(height: screenHeight * 0.02),
                  Row(
                    children: [
                      Container(
                        width: screenWidth * 0.2,
                        height: screenWidth * 0.2,
                        decoration: const BoxDecoration(
                          color: Colors.white,
                          shape: BoxShape.circle,
                        ),
                      ),
                      SizedBox(width: screenWidth * 0.04),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              height: screenWidth * 0.06,
                              width: screenWidth * 0.5,
                              color: Colors.white,
                            ),
                            SizedBox(height: screenHeight * 0.01),
                            Container(
                              height: screenWidth * 0.04,
                              width: screenWidth * 0.3,
                              color: Colors.white,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: screenHeight * 0.02),
                  Row(
                    children: List.generate(
                      4,
                      (index) => Container(
                        margin: EdgeInsets.only(right: screenWidth * 0.02),
                        width: screenWidth * 0.2,
                        height: screenWidth * 0.1,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius:
                              BorderRadius.circular(screenWidth * 0.025),
                        ),
                      ),
                    ),
                  ),
                  SizedBox(height: screenHeight * 0.02),
                  Container(
                    height: screenHeight * 0.1,
                    color: Colors.white,
                  ),
                  SizedBox(height: screenHeight * 0.02),
                  Container(
                    height: screenWidth * 0.06,
                    width: screenWidth * 0.5,
                    color: Colors.white,
                  ),
                  SizedBox(height: screenHeight * 0.02),
                  SizedBox(
                    height: screenHeight * 0.04,
                    child: ListView(
                      scrollDirection: Axis.horizontal,
                      children: List.generate(
                        4,
                        (index) => Container(
                          margin: EdgeInsets.only(right: screenWidth * 0.02),
                          width: screenWidth * 0.25,
                          height: screenWidth * 0.08,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius:
                                BorderRadius.circular(screenWidth * 0.05),
                          ),
                        ),
                      ),
                    ),
                  ),
                  SizedBox(height: screenHeight * 0.02),
                  Container(
                    width: screenWidth * 0.4,
                    height: screenWidth * 0.06,
                    color: Colors.white,
                  ),
                  SizedBox(height: screenHeight * 0.02),
                  SizedBox(
                    height: screenHeight * 0.42,
                    child: ListView(
                      scrollDirection: Axis.horizontal,
                      padding: EdgeInsets.only(right: screenWidth * 0.04),
                      children: List.generate(
                        3,
                        (index) => _buildShimmerDishCard(
                            context, screenWidth, screenHeight),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildShimmerDishCard(
      BuildContext context, double screenWidth, double screenHeight) {
    return Container(
      width: screenWidth * 0.8,
      height: screenHeight * 0.42,
      margin: EdgeInsets.only(right: screenWidth * 0.04),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(screenWidth * 0.04),
      ),
      child: Column(
        children: [
          Container(
            height: screenHeight * 0.25,
            color: Colors.white,
          ),
          Padding(
            padding: EdgeInsets.all(screenWidth * 0.04),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: screenWidth * 0.4,
                  height: screenWidth * 0.05,
                  color: Colors.white,
                ),
                SizedBox(height: screenHeight * 0.01),
                Container(
                  width: screenWidth * 0.3,
                  height: screenWidth * 0.04,
                  color: Colors.white,
                ),
                SizedBox(height: screenHeight * 0.02),
                Container(
                  width: screenWidth * 0.2,
                  height: screenWidth * 0.06,
                  color: Colors.white,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDishCard(BuildContext context, double screenWidth,
      double screenHeight, Map<String, dynamic> dish) {
    final priceDouble = double.tryParse(dish['price'] ?? '0.00') ?? 0.0;
    const rating = "90";

    return GestureDetector(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => DishDetailPage(
                dishId: dish['id'].toString(),
                chefId: chefDetails?.data?.chef?.chefId ?? 0,
              ),
            ),
          );
        },
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(screenWidth * 0.04),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ClipRRect(
                borderRadius: BorderRadius.vertical(
                    top: Radius.circular(screenWidth * 0.04)),
                child: Image.network(
                  ServerHelper.imageUrl + (dish['photo'] ?? ''),
                  height: ten * 15 + twelve,
                  width: double.infinity,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      height: ten * 15 + twelve,
                      width: double.infinity,
                      color: Colors.grey[200],
                      child: const Center(child: Text('Image not available')),
                    );
                  },
                ),
              ),
              Padding(
                padding: EdgeInsets.only(
                    top: screenWidth * 0.025,
                    bottom: screenWidth * 0.015,
                    right: screenWidth * 0.04,
                    left: screenWidth * 0.04),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      dish['name'] ?? 'Unknown Dish',
                      style: TextStyle(
                        fontSize: forteen,
                        fontWeight: FontWeight.w500,
                        color: const Color(0xFF1F2122),
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: screenHeight * 0.015),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Wrap(
                                spacing: screenWidth * 0.02,
                                runSpacing: screenHeight * 0.01,
                                children: [
                                  Container(
                                    padding: EdgeInsets.symmetric(
                                      horizontal: screenWidth * 0.015,
                                      vertical: screenWidth * 0.005,
                                    ),
                                    decoration: BoxDecoration(
                                      color: const Color(0xFFE1E3E6),
                                      borderRadius: BorderRadius.circular(
                                          screenWidth * 0.03),
                                    ),
                                    child: Text(
                                      "${dish['serving_size'].split(' ').first} Servings",
                                      style: TextStyle(
                                        fontSize: ten,
                                        fontWeight: FontWeight.w500,
                                        fontFamily: 'Inter',
                                      ),
                                    ),
                                  ),
                                  Container(
                                    padding: EdgeInsets.symmetric(
                                      horizontal: screenWidth * 0.015,
                                      vertical: screenWidth * 0.005,
                                    ),
                                    decoration: BoxDecoration(
                                      color: const Color(0xFFE1E3E6),
                                      borderRadius: BorderRadius.circular(
                                          screenWidth * 0.03),
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Image.asset(
                                          'assets/icons/thump.png',
                                          width: screenWidth * 0.03,
                                          height: screenWidth * 0.0275,
                                          color: Colors.black54,
                                        ),
                                        SizedBox(width: screenWidth * 0.01),
                                        Text(
                                          "$rating%",
                                          style: TextStyle(
                                            fontSize: ten,
                                            fontWeight: FontWeight.w500,
                                            fontFamily: 'Inter',
                                          ),
                                        ),
                                        SizedBox(width: screenWidth * 0.005),
                                        Text(
                                          "($rating)",
                                          style: TextStyle(
                                            fontSize: ten,
                                            fontWeight: FontWeight.w500,
                                            fontFamily: 'Inter',
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: screenHeight * 0.02),
                              Text.rich(
                                TextSpan(
                                  children: [
                                    TextSpan(
                                      text: '\$',
                                      style: TextStyle(
                                        fontFamily: 'Inter',
                                        fontSize: twelve,
                                        fontWeight: FontWeight.w600,
                                        color: const Color(0xFF1F2122),
                                      ),
                                    ),
                                    TextSpan(
                                      text: priceDouble.toStringAsFixed(2),
                                      style: TextStyle(
                                        fontFamily: 'Roboto',
                                        fontSize: twelve,
                                        fontWeight: FontWeight.w600,
                                        color: const Color(0xFF1F2122),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(
                                height: 4,
                              )
                            ],
                          ),
                        ),
                        BlocListener<AccountBloc, AccountState>(
                          listener: (context, state) {
                            if (state is AddToCartSuccess) {
                              ScaffoldMessenger.of(context)
                                ..hideCurrentSnackBar()
                                ..showSnackBar(
                                    SnackBar(content: Text(state.message)));
                            } else if (state is AddToCartFailed) {
                              ScaffoldMessenger.of(context)
                                ..hideCurrentSnackBar()
                                ..showSnackBar(
                                  SnackBar(
                                    content: Text(state.message),
                                    backgroundColor: Colors.red,
                                  ),
                                );
                            }
                            if (state is GetCartCountSuccess) {
                              setState(() {});
                            }
                          },
                          child: BlocBuilder<AccountBloc, AccountState>(
                            builder: (context, state) {
                              final isLoading = state is AddToCartLoading &&
                                  state.dishId == dish['id'];
                              final bool isInCart = dishesData
                                      ?.data?.featuredList
                                      ?.firstWhere((d) => d.id == dish['id'],
                                          orElse: () => FeaturedList())
                                      .inCart ??
                                  false;

                              return GestureDetector(
                                onTap: isLoading || isInCart
                                    ? null
                                    : () {
                                        if (dish['id'] == 0) {
                                          ScaffoldMessenger.of(context)
                                              .showSnackBar(
                                            const SnackBar(
                                              content: Text('Invalid dish ID'),
                                              backgroundColor: Colors.red,
                                            ),
                                          );
                                          return;
                                        }
                                        if (dish['serving_size_id'] == 0) {
                                          ScaffoldMessenger.of(context)
                                              .showSnackBar(
                                            const SnackBar(
                                              content: Text(
                                                  'Invalid serving size ID'),
                                              backgroundColor: Colors.red,
                                            ),
                                          );
                                          return;
                                        }
                                        context.read<AccountBloc>().add(
                                              AddToCartEvent({
                                                'chef_id': chefDetails
                                                        ?.data?.chef?.chefId ??
                                                    0,
                                                'chef_dish_id': dish['id'],
                                                'quantity': 1,
                                                'serving_size_id':
                                                    dish['serving_size_id'],
                                              }),
                                            );
                                      },
                                child: isLoading
                                    ? SizedBox(
                                        width: forteen + ten,
                                        height: forteen + ten,
                                        child: const CircularProgressIndicator(
                                          strokeWidth: 2,
                                          valueColor:
                                              AlwaysStoppedAnimation<Color>(
                                                  Colors.black),
                                        ),
                                      )
                                    : isInCart
                                        ? Container(
                                            width: forteen + ten,
                                            height: forteen + ten,
                                            decoration: const BoxDecoration(
                                              shape: BoxShape.circle,
                                              color:
                                                  Color.fromARGB(255, 0, 0, 0),
                                            ),
                                            child: Icon(
                                              Icons.check,
                                              color: Colors.white,
                                              size: sixteen,
                                            ),
                                          )
                                        : Image.asset(
                                            'assets/icons/add.png',
                                            width: forteen + ten,
                                            height: forteen + ten,
                                            semanticLabel: 'Add to cart',
                                          ),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ));
  }

  Widget _buildLocationHeader(
      BuildContext context, double screenWidth, double screenHeight) {
    return Column(
      children: [
        Padding(
          padding: EdgeInsets.symmetric(
              horizontal: screenWidth * 0.04, vertical: screenHeight * 0.015),
          child: Row(
            children: [
              GestureDetector(
                onTap: () {
                  setState(() {
                    _showLocationMenu = !_showLocationMenu;
                    _showTimeOptions = false;
                  });
                },
                child: Icon(Icons.menu,
                    size: screenWidth * 0.06,
                    color: const Color.fromRGBO(31, 33, 34, 1)),
              ),
              SizedBox(width: screenWidth * 0.03),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    GestureDetector(
                      onTap: () {
                        setState(() {
                          _showLocationMenu = !_showLocationMenu;
                          _showTimeOptions = false;
                        });
                      },
                      child: Row(
                        children: [
                          Icon(Icons.location_on_outlined,
                              size: screenWidth * 0.035,
                              color: const Color.fromRGBO(31, 33, 34, 1)),
                          SizedBox(width: screenWidth * 0.01),
                          Expanded(
                            child: Text(
                              _currentAddressData?.addressText ??
                                  'Loading address...',
                              style: TextStyle(
                                fontSize: screenWidth * 0.03,
                                fontWeight: FontWeight.w500,
                                fontFamily: 'Inter',
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: screenHeight * 0.005),
                    Row(
                      children: [
                        Icon(Icons.access_time,
                            size: screenWidth * 0.03,
                            color: const Color.fromRGBO(31, 33, 34, 1)),
                        SizedBox(width: screenWidth * 0.015),
                        InkWell(
                          onTap: () {
                            setState(() {
                              _showLocationMenu = true;
                              _showTimeOptions = true;
                            });
                          },
                          child: Row(
                            children: [
                              Text(
                                _selectedDeliveryTime,
                                style: TextStyle(
                                  fontSize: screenWidth * 0.03,
                                  fontWeight: FontWeight.w400,
                                  fontFamily: 'Inter',
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              GestureDetector(
                onTap: _toggleSearchPopup,
                child: Icon(Icons.search,
                    size: screenWidth * 0.06,
                    color: const Color.fromRGBO(31, 33, 34, 1)),
              ),
            ],
          ),
        ),
        if (_showTimeOptions) ...[
          Container(
            color: Colors.white,
            constraints: BoxConstraints(
              maxHeight: screenHeight * 0.6,
            ),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Padding(
                    padding: EdgeInsets.all(screenWidth * 0.04),
                    child: Row(
                      children: [
                        IconButton(
                          icon: Icon(Icons.arrow_back,
                              color: const Color(0xFF1F2122),
                              size: screenWidth * 0.05),
                          onPressed: () {
                            setState(() {
                              _showTimeOptions = false;
                            });
                          },
                        ),
                        Expanded(
                          child: Center(
                            child: Text(
                              'Schedule Delivery',
                              style: TextStyle(
                                fontSize: screenWidth * 0.045,
                                fontWeight: FontWeight.w600,
                                color: const Color(0xFF1F2122),
                              ),
                            ),
                          ),
                        ),
                        SizedBox(width: screenWidth * 0.15),
                      ],
                    ),
                  ),
                  _buildScheduleTimeOptionWidget(
                      context, screenWidth, screenHeight),
                ],
              ),
            ),
          ),
        ] else if (_showLocationMenu) ...[
          Container(
            color: Colors.white,
            constraints: BoxConstraints(
              maxHeight: screenHeight * 0.7,
            ),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Padding(
                    padding: EdgeInsets.all(screenWidth * 0.04),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Enter Your Street And House Number',
                          style: TextStyle(
                            fontSize: screenWidth * 0.035,
                            fontWeight: FontWeight.w500,
                            color: const Color(0xFF1F2122),
                            fontFamily: 'Inter',
                          ),
                        ),
                        SizedBox(height: screenHeight * 0.015),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Container(
                              decoration: BoxDecoration(
                                border: Border.all(
                                    color: const Color(0xFFE1E3E6), width: 1),
                                borderRadius:
                                    BorderRadius.circular(screenWidth * 0.09),
                              ),
                              padding: EdgeInsets.symmetric(
                                  horizontal: screenWidth * 0.045, vertical: 0),
                              height: screenHeight * 0.055,
                              child: Row(
                                children: [
                                  Expanded(
                                    child: TextField(
                                      controller: _addressController,
                                      onChanged: searchPlaces,
                                      decoration: InputDecoration(
                                        hintText: 'Street, Postal code',
                                        hintStyle: TextStyle(
                                          color: const Color(0xFF66696D),
                                          fontSize: screenWidth * 0.035,
                                          fontWeight: FontWeight.w400,
                                          fontFamily: 'Inter',
                                        ),
                                        border: InputBorder.none,
                                        contentPadding: EdgeInsets.zero,
                                        isDense: true,
                                      ),
                                    ),
                                  ),
                                  TextButton.icon(
                                    onPressed: () async {
                                      final position =
                                          await _getCurrentLocation();
                                      if (position != null) {
                                        final List<Placemark> placemarks =
                                            await placemarkFromCoordinates(
                                          position.latitude,
                                          position.longitude,
                                        );
                                        if (placemarks.isNotEmpty) {
                                          final Placemark place = placemarks[0];
                                          final String address =
                                              '${place.street}, ${place.subLocality}, ${place.locality}, ${place.postalCode}';
                                          context.read<AccountBloc>().add(
                                                AddAddressEvent({
                                                  "latitude": position.latitude,
                                                  "longitude":
                                                      position.longitude,
                                                  "address_text": address,
                                                  "is_current": true,
                                                }),
                                              );
                                          setState(() {
                                            _currentLatitude =
                                                position.latitude;
                                            _currentLongitude =
                                                position.longitude;
                                            _showLocationMenu = false;
                                          });
                                          Initializer().setCoordinates(
                                              position.latitude,
                                              position.longitude);
                                          context.read<HomeBloc>().add(
                                                GetHomeDataEvent(
                                                  data: {
                                                    'latitude':
                                                        position.latitude,
                                                    'longitude':
                                                        position.longitude,
                                                  },
                                                ),
                                              );
                                        }
                                      }
                                    },
                                    icon: Icon(Icons.my_location,
                                        size: screenWidth * 0.05,
                                        color: const Color(0xFF1F2122)),
                                    label: Text(
                                      'Locate me',
                                      style: TextStyle(
                                        fontSize: screenWidth * 0.030,
                                        fontWeight: FontWeight.w600,
                                        fontFamily: 'Inter',
                                        color: const Color(0xFF1F2122),
                                        decoration: TextDecoration.underline,
                                      ),
                                    ),
                                    style: TextButton.styleFrom(
                                      padding: EdgeInsets.zero,
                                      minimumSize: Size.zero,
                                      tapTargetSize:
                                          MaterialTapTargetSize.shrinkWrap,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            if (_isSearching)
                              Padding(
                                padding:
                                    EdgeInsets.only(top: screenHeight * 0.01),
                                child: const Center(
                                    child: CircularProgressIndicator()),
                              )
                            else if (_showPredictions &&
                                _searchResults.isNotEmpty)
                              Container(
                                height: screenHeight * 0.15,
                                margin:
                                    EdgeInsets.only(top: screenHeight * 0.01),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius:
                                      BorderRadius.circular(screenWidth * 0.03),
                                  border: Border.all(
                                      color: const Color(0xFFE1E3E6), width: 1),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withOpacity(0.15),
                                      blurRadius: 12,
                                      offset: const Offset(0, 4),
                                    ),
                                  ],
                                ),
                                child: Material(
                                  color: Colors.transparent,
                                  child: ListView.separated(
                                    padding: EdgeInsets.zero,
                                    shrinkWrap: true,
                                    itemCount: _searchResults.length,
                                    separatorBuilder: (_, __) => const Divider(
                                        height: 1, color: Color(0xFFE1E3E6)),
                                    itemBuilder: (_, index) {
                                      final prediction = _searchResults[index];
                                      return ListTile(
                                        dense: true,
                                        contentPadding: EdgeInsets.symmetric(
                                            horizontal: screenWidth * 0.04,
                                            vertical: screenHeight * 0.005),
                                        title: Text(
                                          prediction.description ?? '',
                                          style: TextStyle(
                                            fontSize: screenWidth * 0.035,
                                            fontFamily: 'Inter',
                                          ),
                                        ),
                                        onTap: () => selectPlace(prediction),
                                      );
                                    },
                                  ),
                                ),
                              ),
                          ],
                        ),
                        SizedBox(height: screenHeight * 0.03),
                        Text(
                          'Saved Addresses',
                          style: TextStyle(
                            fontSize: screenWidth * 0.04,
                            fontWeight: FontWeight.w600,
                            fontFamily: 'Inter',
                          ),
                        ),
                        SizedBox(height: screenHeight * 0.02),
                        if (_loadingAddresses)
                          const Center(child: CircularProgressIndicator())
                        else if (_savedAddresses == null ||
                            _savedAddresses!.isEmpty)
                          const Center(child: Text('No saved addresses'))
                        else
                          ...(_savedAddresses!
                              .where((addr) => addr.isCurrent != true)
                              .take(2)
                              .map((address) => _buildAddressListItem(
                                  context, screenWidth, screenHeight, address))
                              .toList()),
                        SizedBox(height: screenHeight * 0.02),
                        InkWell(
                          onTap: () {
                            setState(() {
                              _showTimeOptions = true;
                            });
                          },
                          child: Row(
                            children: [
                              Icon(Icons.schedule, size: screenWidth * 0.06),
                              SizedBox(width: screenWidth * 0.04),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Time preference',
                                      style: TextStyle(
                                        fontSize: screenWidth * 0.035,
                                        fontWeight: FontWeight.w500,
                                        fontFamily: 'Inter',
                                        color: const Color(0xFF1F2122),
                                      ),
                                    ),
                                    Text(
                                      _selectedDeliveryTime,
                                      style: TextStyle(
                                        fontSize: screenWidth * 0.03,
                                        color: Colors.grey,
                                        fontFamily: 'Inter',
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Icon(
                                Icons.arrow_forward_ios,
                                size: screenWidth * 0.03,
                                color: const Color(0xFF1F2122),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
        const Divider(height: 2, color: Color.fromARGB(255, 219, 212, 212)),
      ],
    );
  }

  Widget _buildAddressListItem(BuildContext context, double screenWidth,
      double screenHeight, AddressData address) {
    return InkWell(
      onTap: () => _selectAddress(address),
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: screenHeight * 0.01),
        child: Row(
          children: [
            Icon(Icons.location_on_outlined, size: screenWidth * 0.06),
            SizedBox(width: screenWidth * 0.04),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    address.addressText ?? 'No address text',
                    style: TextStyle(
                      fontSize: screenWidth * 0.035,
                      fontWeight: FontWeight.w400,
                      color: const Color(0xFF1F2122),
                      fontFamily: 'Inter',
                    ),
                  ),
                  if (address.isCurrent == true)
                    Text(
                      'Current address',
                      style: TextStyle(
                        fontSize: screenWidth * 0.03,
                        color: Colors.green,
                      ),
                    ),
                ],
              ),
            ),
            Icon(Icons.arrow_forward_ios,
                size: screenWidth * 0.03, color: const Color(0xFF1F2122)),
          ],
        ),
      ),
    );
  }

  void _toggleSearchPopup() {
    if (_isSearchPopupVisible) {
      _closeSearchPopup();
    } else {
      _showSearchPopup();
    }
  }

  void _showSearchPopup() {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final offset = renderBox.localToGlobal(Offset.zero);

    _overlayEntry = OverlayEntry(
        builder: (context) => Positioned(
            top: offset.dy + screenHeight * 0.08,
            left: screenWidth * 0.02,
            right: screenWidth * 0.02,
            child: Material(
              elevation: 8,
              borderRadius: BorderRadius.circular(screenWidth * 0.03),
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(screenWidth * 0.03),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Padding(
                      padding: EdgeInsets.symmetric(
                          horizontal: screenWidth * 0.03,
                          vertical: screenHeight * 0.01),
                      child: Row(
                        children: [
                          Expanded(
                            child: Row(
                              children: [
                                SizedBox(width: screenWidth * 0.05),
                                Padding(
                                  padding: EdgeInsets.only(
                                      right: screenWidth * 0.01),
                                  child: Icon(Icons.search,
                                      color: const Color(0xFF414346),
                                      size: screenWidth * 0.05),
                                ),
                                Expanded(
                                  child: TextField(
                                    decoration: InputDecoration(
                                      hintText:
                                          'Search Dishes, Chefs, cuisines...',
                                      hintStyle: TextStyle(
                                        fontFamily: 'Inter',
                                        fontWeight: FontWeight.w400,
                                        fontSize: screenWidth * 0.035,
                                        color: const Color(0xFF909090),
                                      ),
                                      border: InputBorder.none,
                                      contentPadding: EdgeInsets.zero,
                                      isDense: true,
                                    ),
                                    autofocus: true,
                                    onChanged: searchPlaces,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              IconButton(
                                padding: EdgeInsets.zero,
                                visualDensity: VisualDensity.compact,
                                constraints: BoxConstraints.tightFor(
                                    width: screenWidth * 0.05,
                                    height: screenHeight * 0.05),
                                icon: Icon(Icons.cancel_outlined,
                                    color: const Color(0xFF1F2122),
                                    size: screenWidth * 0.045),
                                onPressed: () {
                                  setState(() {
                                    _addressController.clear();
                                    _searchResults = [];
                                    _showPredictions = false;
                                  });
                                },
                              ),
                              IconButton(
                                padding: EdgeInsets.zero,
                                visualDensity: VisualDensity.compact,
                                constraints: BoxConstraints.tightFor(
                                    width: screenWidth * 0.05,
                                    height: screenHeight * 0.05),
                                icon: Icon(Icons.close,
                                    color: const Color(0xFF1F2122),
                                    size: screenWidth * 0.06),
                                onPressed: _closeSearchPopup,
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    const Divider(height: 1, thickness: 1),
                    Container(
                      constraints:
                          BoxConstraints(maxHeight: screenHeight * 0.4),
                      child: SingleChildScrollView(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: EdgeInsets.symmetric(
                                  horizontal: screenWidth * 0.03),
                              child: Row(
                                children: [
                                  _buildSearchTab(
                                      context, screenWidth, 'All', true),
                                  _buildSearchTab(
                                      context, screenWidth, 'Dishes', false),
                                  _buildSearchTab(
                                      context, screenWidth, 'Chefs', false),
                                ],
                              ),
                            ),
                            if (_showPredictions && _searchResults.isNotEmpty)
                              ..._searchResults.map((prediction) => ListTile(
                                    dense: true,
                                    contentPadding: EdgeInsets.symmetric(
                                        horizontal: screenWidth * 0.04,
                                        vertical: screenHeight * 0.005),
                                    title: Text(
                                      prediction.description ?? '',
                                      style: TextStyle(
                                        fontSize: screenWidth * 0.035,
                                        fontFamily: 'Inter',
                                      ),
                                    ),
                                    onTap: () => selectPlace(prediction),
                                  ))
                            else ...[
                              ListTile(
                                leading: Image.asset(
                                  'assets/icons/recent.png',
                                  width: screenWidth * 0.06,
                                  height: screenWidth * 0.06,
                                  color: const Color(0xFF1F2122),
                                ),
                                title: Text('Recent',
                                    style: TextStyle(
                                        fontWeight: FontWeight.w600,
                                        fontSize: screenWidth * 0.04,
                                        fontFamily: 'Inter')),
                                dense: true,
                                horizontalTitleGap: screenWidth * 0.015,
                                contentPadding: EdgeInsets.symmetric(
                                    horizontal: screenWidth * 0.04,
                                    vertical: 0),
                              ),
                              _buildSearchItem(context, screenWidth, 'Chinese'),
                              _buildSearchItem(context, screenWidth, 'Chicken'),
                              _buildSearchItem(context, screenWidth, 'Asian'),
                              Padding(
                                padding: EdgeInsets.fromLTRB(
                                    screenWidth * 0.04,
                                    screenHeight * 0.02,
                                    screenWidth * 0.04,
                                    screenHeight * 0.01),
                                child: Text('Popular searches',
                                    style: TextStyle(
                                        fontFamily: 'Inter',
                                        fontWeight: FontWeight.w600,
                                        fontSize: screenWidth * 0.04,
                                        color: const Color(0xFF1F2122))),
                              ),
                              Padding(
                                padding: EdgeInsets.fromLTRB(screenWidth * 0.04,
                                    0, screenWidth * 0.04, screenHeight * 0.02),
                                child: Wrap(
                                  spacing: screenWidth * 0.02,
                                  runSpacing: screenHeight * 0.01,
                                  children: [
                                    _buildSearchTag(
                                        context, screenWidth, 'Pizza'),
                                    _buildSearchTag(
                                        context, screenWidth, 'Burger'),
                                    _buildSearchTag(
                                        context, screenWidth, 'Curry'),
                                    _buildSearchTag(
                                        context, screenWidth, 'Tacos'),
                                  ],
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            )));
    Overlay.of(context).insert(_overlayEntry!);
    _isSearchPopupVisible = true;
  }

  void _closeSearchPopup() {
    if (_overlayEntry != null) {
      _overlayEntry!.remove();
      _overlayEntry = null;
      _isSearchPopupVisible = false;
    }
  }

  Widget _buildSearchTab(
      BuildContext context, double screenWidth, String title, bool isActive) {
    return Padding(
      padding: EdgeInsets.symmetric(
          vertical: screenWidth * 0.025, horizontal: screenWidth * 0.0175),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            height: screenWidth * 0.05,
            alignment: Alignment.center,
            child: Text(
              title,
              style: TextStyle(
                color: isActive
                    ? const Color(0xFF1F2122)
                    : const Color(0xFF66696D),
                fontWeight: FontWeight.w600,
                fontSize: screenWidth * 0.035,
              ),
            ),
          ),
          SizedBox(height: screenWidth * 0.0075),
          Container(
            height: 3,
            width: screenWidth * 0.05,
            color: isActive ? Colors.amber : Colors.transparent,
          ),
        ],
      ),
    );
  }

  Widget _buildSearchItem(
      BuildContext context, double screenWidth, String title) {
    return Padding(
      padding: EdgeInsets.symmetric(
          vertical: screenWidth * 0.02, horizontal: screenWidth * 0.04),
      child: Text(
        title,
        style: TextStyle(
          fontSize: screenWidth * 0.035,
          fontFamily: 'Inter',
          fontWeight: FontWeight.w400,
          color: const Color(0xFF1F2122),
        ),
      ),
    );
  }

  Widget _buildSearchTag(
      BuildContext context, double screenWidth, String title) {
    return Container(
      padding: EdgeInsets.symmetric(
          horizontal: screenWidth * 0.03, vertical: screenWidth * 0.0125),
      decoration: BoxDecoration(
        color: const Color(0xFFE1E3E6),
        borderRadius: BorderRadius.circular(screenWidth * 0.04),
      ),
      child: Text(
        title,
        style: TextStyle(
          fontSize: screenWidth * 0.035,
          fontFamily: 'Inter',
          fontWeight: FontWeight.w600,
          color: const Color(0xFF1F2122),
        ),
      ),
    );
  }

  Widget _buildScheduleTimeOptionWidget(
      BuildContext context, double screenWidth, double screenHeight) {
    if (_availableTimings == null || _availableTimings!.isEmpty) {
      return Center(
        child: Padding(
          padding: EdgeInsets.all(screenWidth * 0.04),
          child: Text(
            'No time slots available',
            style: TextStyle(
              fontSize: screenWidth * 0.035,
              fontWeight: FontWeight.w400,
              fontFamily: 'Inter',
              color: const Color(0xFF1F2122),
            ),
          ),
        ),
      );
    }

    return BlocBuilder<MealplanBloc, MealPlanState>(
      builder: (context, state) {
        return Column(
          children: [
            Column(
              children: _availableTimings!.map((timing) {
                final timeSlot =
                    '${_formatTimeToAMPM(timing.startTime)} - ${_formatTimeToAMPM(timing.endTime)}';
                return InkWell(
                  onTap: () {
                    setState(() {
                      _currentlySelectedTimeRange = timeSlot;
                      _selectedTimeId = timing.id;
                    });
                  },
                  child: Container(
                    padding: EdgeInsets.symmetric(
                        horizontal: screenWidth * 0.06,
                        vertical: screenHeight * 0.015),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          timeSlot,
                          style: TextStyle(
                            fontSize: screenWidth * 0.035,
                            fontWeight: FontWeight.w400,
                            fontFamily: 'Inter',
                            color: const Color(0xFF1F2122),
                          ),
                        ),
                        if (timeSlot == _currentlySelectedTimeRange)
                          Icon(Icons.check,
                              color: const Color(0xFF1F2122),
                              size: screenWidth * 0.05),
                      ],
                    ),
                  ),
                );
              }).toList(),
            ),
            Padding(
              padding: EdgeInsets.all(screenWidth * 0.04),
              child: Column(
                children: [
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: state is AddTimePreferencesLoading
                          ? null
                          : () {
                              if (_selectedTimeId != null) {
                                context.read<MealplanBloc>().add(
                                      AddTimePreferences({
                                        "time_preference_id": _selectedTimeId
                                      }),
                                    );
                              }
                            },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.black,
                        shape: RoundedRectangleBorder(
                          borderRadius:
                              BorderRadius.circular(screenWidth * 0.06),
                        ),
                        padding:
                            EdgeInsets.symmetric(vertical: screenWidth * 0.03),
                      ),
                      child: state is AddTimePreferencesLoading
                          ? SizedBox(
                              height: screenWidth * 0.05,
                              width: screenWidth * 0.05,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor:
                                    AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            )
                          : Text(
                              'Schedule',
                              style: TextStyle(
                                fontSize: screenWidth * 0.035,
                                fontWeight: FontWeight.w600,
                                color: Colors.white,
                                fontFamily: 'Inter',
                              ),
                            ),
                    ),
                  ),
                  SizedBox(
                    width: double.infinity,
                    child: OutlinedButton(
                      onPressed: () {
                        context.read<MealplanBloc>().add(
                              AddTimePreferences({"is_deliver_now": true}),
                            );
                      },
                      style: OutlinedButton.styleFrom(
                        backgroundColor: Colors.white,
                        side: const BorderSide(color: Color(0xFFE1E3E6)),
                        shape: RoundedRectangleBorder(
                          borderRadius:
                              BorderRadius.circular(screenWidth * 0.06),
                        ),
                        padding:
                            EdgeInsets.symmetric(vertical: screenWidth * 0.03),
                      ),
                      child: Text(
                        'Deliver Now',
                        style: TextStyle(
                          fontSize: screenWidth * 0.035,
                          fontWeight: FontWeight.w600,
                          color: const Color(0xFF1F2122),
                          fontFamily: 'Inter',
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  String _formatTimeToAMPM(String? time) {
    if (time == null) return '';
    try {
      final parts = time.split(':');
      if (parts.length < 2) return time;
      int hour = int.parse(parts[0]);
      int minute = int.parse(parts[1]);
      String period = hour >= 12 ? 'PM' : 'AM';
      if (hour > 12) hour -= 12;
      if (hour == 0) hour = 12;
      return '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')} $period';
    } catch (e) {
      return time;
    }
  }

  Future<Position?> _getCurrentLocation() async {
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Location services are disabled')),
        );
        return null;
      }
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Location permissions are denied')),
          );
          return null;
        }
      }
      if (permission == LocationPermission.deniedForever) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Location permissions are permanently denied'),
          ),
        );
        return null;
      }
      return await Geolocator.getCurrentPosition();
    } catch (e) {
      log('Error getting current location: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error getting location: $e')),
      );
      return null;
    }
  }

  Future<void> searchPlaces(String query) async {
    if (query.isEmpty) {
      setState(() {
        _searchResults = [];
        _showPredictions = false;
      });
      return;
    }
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 300), () async {
      setState(() => _isSearching = true);
      try {
        final url = Uri.parse(
          'https://maps.googleapis.com/maps/api/place/autocomplete/json?input=$query&key=$kGoogleApiKey',
        );
        final response = await http.get(url);
        if (response.statusCode == 200) {
          final json = jsonDecode(response.body);
          if (json['status'] == 'OK') {
            final predictions = json['predictions'] as List;
            setState(() {
              _searchResults = predictions
                  .map((p) => Prediction(
                        description: p['description'],
                        placeId: p['place_id'],
                      ))
                  .toList();
              _showPredictions = true;
            });
          } else {
            throw Exception('Places API error: ${json['status']}');
          }
        } else {
          throw Exception('Failed to fetch places: ${response.statusCode}');
        }
      } catch (e) {
        log('Error searching places: $e');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to search places: $e')),
        );
      } finally {
        setState(() => _isSearching = false);
      }
    });
  }

  Future<void> selectPlace(Prediction prediction) async {
    setState(() => _isSearching = true);
    try {
      final url = Uri.parse(
        'https://maps.googleapis.com/maps/api/place/details/json?place_id=${prediction.placeId}&fields=geometry&key=$kGoogleApiKey',
      );
      final response = await http.get(url);
      if (response.statusCode == 200) {
        final json = jsonDecode(response.body);
        if (json['status'] == 'OK') {
          final location = json['result']['geometry']['location'];
          final lat = location['lat'];
          final lng = location['lng'];
          context.read<AccountBloc>().add(
                AddAddressEvent({
                  "latitude": lat,
                  "longitude": lng,
                  "address_text": prediction.description,
                  "is_current": true,
                }),
              );
          setState(() {
            _currentLatitude = lat;
            _currentLongitude = lng;
            _addressController.text = prediction.description ?? '';
            _showPredictions = false;
            _searchResults = [];
            _showLocationMenu = false;
          });
          Initializer().setCoordinates(lat, lng);
          context.read<HomeBloc>().add(
                GetHomeDataEvent(
                  data: {
                    'latitude': lat,
                    'longitude': lng,
                  },
                ),
              );
        } else {
          throw Exception('Place details API error: ${json['status']}');
        }
      } else {
        throw Exception(
            'Failed to fetch place details: ${response.statusCode}');
      }
    } catch (e) {
      log('Error selecting place: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to select place: $e')),
      );
    } finally {
      setState(() => _isSearching = false);
    }
  }
}

class Prediction {
  final String? description;
  final String? placeId;

  Prediction({this.description, this.placeId});
}
