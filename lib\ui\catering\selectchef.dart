import 'dart:async';
import 'dart:developer';

import 'package:db_eats/bloc/mealplan_bloc.dart';
import 'package:db_eats/bloc/catering_bloc.dart';
import 'package:db_eats/data/models/meal_plan/filterchefsmodel.dart';
import 'package:db_eats/server/serverhelper.dart';
import 'package:db_eats/ui/home2.dart';
import 'package:db_eats/ui/mainnavigationpage.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shimmer/shimmer.dart';
import 'package:fluttertoast/fluttertoast.dart';

class SelectChef extends StatefulWidget {
  final Map<String, dynamic>? userData;
  final dynamic chefData;
  final int? catering_id; // Make catering_id optional

  const SelectChef({
    super.key,
    this.userData,
    this.chefData,
    this.catering_id, // Named optional parameter
  });

  @override
  State<SelectChef> createState() => _SelectChefState();
}

class _SelectChefState extends State<SelectChef> {
  List<dynamic> _chefs = [];
  bool _isLoading = true;
  int? _selectedChefId;
  dynamic _selectedChef;

  @override
  void initState() {
    super.initState();
    _loadChefData();
  }

  late double ten;
  late double twelve;
  late double forteen;
  late double sixteen;
  late double eighteen;
  late double twenty;
  late double twentyFour;
  late double screenWidth;
  late double screenHeight;
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    screenWidth = MediaQuery.of(context).size.width;
    screenHeight = MediaQuery.of(context).size.height;

    ten = screenWidth * 0.02545;
    twelve = screenWidth * 0.03054;
    forteen = screenWidth * 0.035;
    sixteen = screenWidth * 0.04073;
    eighteen = screenWidth * 0.04582;
    twenty = screenWidth * 0.05091;
    twentyFour = screenWidth * 0.06109;
  }

  void _loadChefData() {
    setState(() {
      _isLoading = true;
    });

    try {
      if (widget.chefData != null && widget.chefData['chefs'] != null) {
        _chefs = widget.chefData['chefs'];
      }
    } catch (e) {
      print('Error loading chef data: $e');
      _chefs = [];
    }

    setState(() {
      _isLoading = false;
    });
  }

  void _onChefSelected(dynamic chef) {
    setState(() {
      _selectedChefId = chef['chef_id'];
      _selectedChef = chef;
    });
  }

  void _confirmChef() {
    if (_selectedChefId == null || widget.userData == null) {
      Fluttertoast.showToast(
        msg: "Please select a chef",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: Colors.red,
        textColor: Colors.white,
      );
      return;
    }

    // Prepare the request data
    Map<String, dynamic> requestData = {
      "chef_id": _selectedChefId,
      "cuisine_ids": widget.userData!["cuisine_ids"],
      "sub_cuisine_ids": widget.userData!["sub_cuisine_ids"],
      "local_cuisine_ids": widget.userData!["local_cuisine_ids"],
      "people_count": widget.userData!["people_count"],
      "date": widget.userData!["date"],
      //  "delivery_time_id": widget.userData!["delivery_time_id"],
      "address": widget.userData!["address"],
      "state": widget.userData!["state"],
      "city": widget.userData!["city"],
      "zip_code": widget.userData!["zip_code"],
      "packaging_type_id": widget.userData!["packaging_type_id"],
      "catering_type_id": widget.userData!["catering_type_id"],
      "dietary_preference_id": widget.userData!["dietary_preference_id"],
      "spice_level_id": widget.userData!["spice_level_id"],
      "allergy_prference_text": widget.userData!["allergy_prference_text"],
      "time_slot_id": widget.userData!["time_slot_id"],
      "latitude": widget.userData!["latitude"],
      "longitude": widget.userData!["longitude"],
    };

    // Dispatch the event to CateringBloc
    if (widget.catering_id != null) {
      requestData["catering_id"] = widget.catering_id;
      context.read<CateringBloc>().add(EditCateringRequestEvent(
            requestData,
          ));
    } else {
      context.read<CateringBloc>().add(AddCateringRequestEvent(requestData));
    }
  }

  Widget _buildChefShimmerGrid() {
    return Column(
      children: List.generate(
        3,
        (index) => Padding(
          padding: EdgeInsets.fromLTRB(sixteen, 0, sixteen, sixteen),
          child: Shimmer.fromColors(
            baseColor: Colors.grey[300]!,
            highlightColor: Colors.grey[100]!,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(sixteen),
              ),
              child: Padding(
                padding: EdgeInsets.all(sixteen),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          width: 40,
                          height: 40,
                          decoration: const BoxDecoration(
                            color: Colors.white,
                            shape: BoxShape.circle,
                          ),
                        ),
                        SizedBox(width: twelve),
                        Container(
                          width: 150,
                          height: 20,
                          color: Colors.white,
                        ),
                      ],
                    ),
                    SizedBox(height: twelve),
                    Container(
                      width: ten * 8,
                      height: twentyFour,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(twelve),
                      ),
                    ),
                    SizedBox(height: twelve),
                    Container(
                      width: double.infinity,
                      height: sixteen,
                      color: Colors.white,
                    ),
                    SizedBox(height: eighteen),
                    Container(
                      width: 120,
                      height: 14,
                      color: Colors.white,
                    ),
                    SizedBox(height: twelve),
                    Container(
                      width: double.infinity,
                      height: 40,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(28),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNoChefMessage() {
    return Container(
      margin: EdgeInsets.fromLTRB(sixteen, 32, sixteen, sixteen),
      padding: EdgeInsets.all(twentyFour),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(sixteen),
        border: Border.all(color: const Color(0xFFE1DDD5)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Image.asset(
            'assets/images/cooking.png',
            width: ten * 9,
            height: ten * 9,
          ),
          SizedBox(height: sixteen),
          Text(
            'No Chefs Available',
            style: TextStyle(
              fontSize: eighteen,
              fontWeight: FontWeight.w600,
              fontFamily: 'Inter',
              color: Color(0xFF1F2122),
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Sorry, there are no chefs available in your area at the moment.',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w400,
              fontFamily: 'Inter',
              color: Color(0xFF414346),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildConfirmButton(CateringState cateringState) {
    return Container(
      padding: EdgeInsets.all(sixteen),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(color: Color(0xFFE1DDD5), width: 1),
        ),
      ),
      child: SafeArea(
        child: SizedBox(
          height: 55,
          width: double.infinity,
          child: ElevatedButton(
            onPressed: cateringState is AddCateringRequestLoading
                ? null
                : (_selectedChefId != null ? _confirmChef : null),
            style: ElevatedButton.styleFrom(
              backgroundColor: _selectedChefId != null
                  ? const Color(0xFF1F2122)
                  : const Color(0xFFE1DDD5),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(32),
              ),
            ),
            child: cateringState is AddCateringRequestLoading
                ? SizedBox(
                    width: twentyFour,
                    height: twentyFour,
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation(Colors.white),
                      strokeWidth: 3,
                    ),
                  )
                : Text(
                    'Confirm Chef',
                    style: TextStyle(
                      fontFamily: 'Inter',
                      fontSize: sixteen,
                      fontWeight: FontWeight.w600,
                      letterSpacing: 0.32,
                      color: _selectedChefId != null
                          ? Colors.white
                          : const Color(0xFF414346),
                    ),
                  ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<CateringBloc, CateringState>(
      listener: (context, cateringState) {
        if (cateringState is AddCateringRequestSuccess) {
          // Fluttertoast.showToast(
          //   msg: "Request sent successfully!",
          //   toastLength: Toast.LENGTH_SHORT,
          //   gravity: ToastGravity.BOTTOM,
          //   backgroundColor: Colors.green,
          //   textColor: Colors.white,
          // );
          // // Navigate to home2
          // Navigator.push(
          //     context,
          //     MaterialPageRoute(
          //         builder: (context) => const MainNavigationScreen(
          //               startIndex: 2,
          //             )));

          showDialog(
            context: context,
            barrierDismissible: false, // Prevent dismissing by tapping outside
            barrierColor:
                Color.fromARGB(75, 0, 0, 0), // Semi-transparent black overlay
            builder: (BuildContext context) {
              return AlertDialog(
                backgroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(twelve),
                ),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(
                      Icons.check_circle,
                      color: Colors.black,
                      size: 60,
                    ),
                    SizedBox(height: sixteen),
                    const Text(
                      "Success!",
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      "Add Request sent successfully!",
                      textAlign: TextAlign.center,
                      style: TextStyle(fontSize: sixteen),
                    ),
                  ],
                ),
              );
            },
          );

          // Auto close after 2 seconds and navigate
          Timer(const Duration(milliseconds: 1500), () {
            Navigator.pop(context); // Close the dialog
            //  Navigator.pop(context); // Pop current screen
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const MainNavigationScreen(startIndex: 2),
              ),
            );
          });
        } else if (cateringState is AddCateringRequestFailed) {
          Fluttertoast.showToast(
            msg: "Failed to send request. Please try again.",
            toastLength: Toast.LENGTH_SHORT,
            gravity: ToastGravity.BOTTOM,
            backgroundColor: Colors.red,
            textColor: Colors.white,
          );
        } else if (cateringState is EditCateringRequestSuccess) {
          // Fluttertoast.showToast(
          //   msg: "Edit Request sent successfully!",
          //   toastLength: Toast.LENGTH_SHORT,
          //   gravity: ToastGravity.BOTTOM,
          //   backgroundColor: Colors.green,
          //   textColor: Colors.white,
          // );
          // Navigate to home2
          log("edit success");

          showDialog(
            context: context,
            barrierDismissible: false, // Prevent dismissing by tapping outside
            barrierColor:
                Color.fromARGB(75, 0, 0, 0), // Semi-transparent black overlay
            builder: (BuildContext context) {
              return AlertDialog(
                backgroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(twelve),
                ),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(
                      Icons.check_circle,
                      color: Colors.black,
                      size: 60,
                    ),
                    SizedBox(height: sixteen),
                    const Text(
                      "Success!",
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      "Edit Request sent successfully!",
                      textAlign: TextAlign.center,
                      style: TextStyle(fontSize: sixteen),
                    ),
                  ],
                ),
              );
            },
          );

          // Auto close after 2 seconds and navigate
          Timer(const Duration(milliseconds: 1500), () {
            Navigator.pop(context); // Close the dialog
            //  Navigator.pop(context); // Pop current screen
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const MainNavigationScreen(startIndex: 2),
              ),
            );
          });
        } else if (cateringState is EditCateringRequestFailed) {
          Fluttertoast.showToast(
            msg: "Failed to send edit request. Please try again.",
            toastLength: Toast.LENGTH_SHORT,
            gravity: ToastGravity.BOTTOM,
            backgroundColor: Colors.red,
            textColor: Colors.white,
          );
        }
      },
      builder: (context, cateringState) {
        return BlocConsumer<MealplanBloc, MealPlanState>(
          listener: (context, state) {
            // Handle state changes if needed
          },
          builder: (context, state) {
            return Scaffold(
              backgroundColor: const Color(0xFFF6F3EC),
              appBar: AppBar(
                backgroundColor: const Color(0xFFF6F3EC),
                elevation: 0,
                scrolledUnderElevation: 0,
                foregroundColor: const Color(0xFF1F2122),
                surfaceTintColor: Colors.transparent,
                leading: IconButton(
                  icon: const Icon(Icons.arrow_back, color: Color(0xFF1F2122)),
                  onPressed: () => Navigator.of(context).pop(),
                ),
                title: const Text(''),
                centerTitle: false,
              ),
              body: Column(
                children: [
                  Expanded(
                    child: ListView(
                      children: [
                        // Select Chef title
                        Padding(
                          padding:
                              EdgeInsets.fromLTRB(sixteen, 0, sixteen, sixteen),
                          child: Text(
                            "Select Chef",
                            style: TextStyle(
                              fontSize: twentyFour,
                              fontWeight: FontWeight.w600,
                              fontFamily: 'Inter',
                              color: Color(0xFF1F2122),
                            ),
                          ),
                        ),
                        SizedBox(height: twelve),

                        // Chef Cards with shimmer
                        _isLoading
                            ? _buildChefShimmerGrid()
                            : _chefs.isEmpty
                                ? _buildNoChefMessage()
                                : Column(
                                    children: _chefs
                                        .map((chef) => _buildChefCard(chef))
                                        .toList(),
                                  ),
                      ],
                    ),
                  ),

                  // Confirm Button at bottom
                  if (!_isLoading && _chefs.isNotEmpty)
                    _buildConfirmButton(cateringState),
                ],
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildChefCard(dynamic chef) {
    final bool isSelected = _selectedChefId == chef['chef_id'];

    return InkWell(
      onTap: () => _onChefSelected(chef),
      child: Container(
        margin: EdgeInsets.fromLTRB(sixteen, 0, sixteen, sixteen),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(sixteen),
          border: isSelected
              ? Border.all(color: const Color(0xFF1F2122), width: 2)
              : Border.all(
                  color: const Color.fromARGB(255, 255, 255, 255), width: 2),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Stack(
          children: [
            Padding(
              padding: EdgeInsets.all(sixteen),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      CircleAvatar(
                        radius: 20,
                        backgroundImage: chef['profile_photo'] != null
                            ? NetworkImage(
                                ServerHelper.imageUrl + chef['profile_photo'])
                            : const AssetImage(
                                    'assets/images/no_image_avatar.png')
                                as ImageProvider,
                      ),
                      SizedBox(width: twelve),
                      Expanded(
                        child: Text(
                          '${chef['chef']['first_name'] ?? ''} ${chef['chef']['last_name'] ?? ''}'
                              .trim(),
                          style: TextStyle(
                            fontSize: eighteen,
                            fontWeight: FontWeight.w600,
                            fontFamily: 'Inter',
                            color: Color(0xFF1F2122),
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: twelve),
                  // Rating
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: const Color(0xFFE1E3E6),
                      borderRadius: BorderRadius.circular(twelve),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Image.asset(
                          'assets/icons/thump.png',
                          width: 11,
                          height: 10,
                          color: const Color(0xFF1F2122),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          "82% (49)",
                          style: TextStyle(
                            fontSize: twelve,
                            fontWeight: FontWeight.w500,
                            fontFamily: 'Inter',
                            color: Color(0xFF1F2122),
                            height: 1.0,
                            letterSpacing: 0.02,
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: twelve),
                  Text(
                    (chef['search_tags'] as List<dynamic>?)?.join(", ") ?? '',
                    style: TextStyle(
                      fontSize: forteen,
                      fontWeight: FontWeight.w400,
                      fontFamily: 'Inter',
                      color: Color(0xFF414346),
                      height: 20 / 14,
                    ),
                  ),
                  SizedBox(height: twelve),

                  SizedBox(
                    height:
                        ten * 15 + eighteen, // Fixed height for dishes section
                    child: ListView.builder(
                      scrollDirection: Axis.horizontal,
                      itemCount:
                          (chef['chef']['catering_dishes'] as List<dynamic>?)
                                  ?.length ??
                              0,
                      itemBuilder: (context, index) {
                        final dish = chef['chef']['catering_dishes'][index];
                        return Container(
                          width: 168,
                          margin: EdgeInsets.only(
                            right: index ==
                                    ((chef['chef']['catering_dishes']
                                                    as List<dynamic>?)
                                                ?.length ??
                                            0) -
                                        1
                                ? 0
                                : 8,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: const Color(0xFFE1DDD5)),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              ClipRRect(
                                borderRadius: const BorderRadius.vertical(
                                  top: Radius.circular(8),
                                ),
                                child: dish['photo'] != null
                                    ? Image.network(
                                        '${ServerHelper.imageUrl}${dish['photo']}',
                                        height: ten * twelve,
                                        width: double.infinity,
                                        fit: BoxFit.cover,
                                        errorBuilder:
                                            (context, error, stackTrace) {
                                          return Container(
                                            height: ten * twelve,
                                            color: const Color(0xFFE1DDD5),
                                            child: const Center(
                                              child: Icon(Icons.restaurant_menu,
                                                  color: Color(0xFF1F2122)),
                                            ),
                                          );
                                        },
                                      )
                                    : Container(
                                        height: ten * twelve,
                                        color: const Color(0xFFE1DDD5),
                                        child: const Center(
                                          child: Icon(Icons.restaurant_menu,
                                              color: Color(0xFF1F2122)),
                                        ),
                                      ),
                              ),
                              Padding(
                                padding: EdgeInsets.all(twelve),
                                child: Text(
                                  dish['title'] ?? '',
                                  style: TextStyle(
                                    fontSize: forteen,
                                    fontWeight: FontWeight.w500,
                                    color: Color(0xFF1F2122),
                                    height: 1.43,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),

            // Selection indicator
            if (isSelected)
              Positioned(
                top: sixteen,
                right: sixteen,
                child: Container(
                  width: twentyFour,
                  height: twentyFour,
                  decoration: const BoxDecoration(
                    color: Color(0xFF1F2122),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.check,
                    color: Colors.white,
                    size: sixteen,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
