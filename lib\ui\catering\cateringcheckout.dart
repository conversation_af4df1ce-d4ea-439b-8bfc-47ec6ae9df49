import 'package:db_eats/bloc/catering_bloc.dart';
import 'package:db_eats/bloc/mealplan_bloc.dart';
import 'package:db_eats/common/initializer.dart';
import 'package:db_eats/data/models/catering/viewcateringrequestsmodel.dart';
import 'package:db_eats/data/models/meal_plan/dropoffoptionmodel.dart';
import 'package:db_eats/ui/catering/editaddress_checkout.dart';
import 'package:db_eats/ui/mainnavigationpage.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shimmer/shimmer.dart';

class CateringCheckoutPage extends StatefulWidget {
  final int cateringId;

  const CateringCheckoutPage({super.key, required this.cateringId});

  @override
  State<CateringCheckoutPage> createState() => _CateringCheckoutPageState();
}

class _CateringCheckoutPageState extends State<CateringCheckoutPage> {
  DropoffOptionItem? _selectedDropoffOption;
  List<DropoffOptionItem> _dropoffOptions = [];
  String? _dropOffInstructions;
  bool _showDropoffError = false;
  bool _isPlacingOrder = false;
  late TextEditingController _instructionsController;

  // Address fields
  String _streetAddress = '';
  String _state = '';
  String _city = '';
  String _zipCode = '';
  bool _showAddressError = false;
  String? _dropoffErrorMessage;

  String _date = '';
  String _startTime = '';
  String _endTime = '';

  bool _addressWasEdited = false;

  @override
  void initState() {
    super.initState();
    _instructionsController = TextEditingController();
    context.read<MealplanBloc>().add(ListDropoffOptionEvent());
    context.read<CateringBloc>().add(ViewCateringRequest(widget.cateringId));
  }

  late double ten;
  late double twelve;
  late double forteen;
  late double sixteen;
  late double eighteen;
  late double twenty;
  late double twentyFour;
  late double screenWidth;
  late double screenHeight;
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    screenWidth = MediaQuery.of(context).size.width;
    screenHeight = MediaQuery.of(context).size.height;

    ten = screenWidth * 0.02545;
    twelve = screenWidth * 0.03054;
    forteen = screenWidth * 0.035;
    sixteen = screenWidth * 0.04073;
    eighteen = screenWidth * 0.04582;
    twenty = screenWidth * 0.05091;
    twentyFour = screenWidth * 0.06109;
  }

  @override
  void dispose() {
    _instructionsController.dispose();
    super.dispose();
  }

  Widget _buildLoadingShimmer(Size size) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        height: size.height * 0.025,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(size.width * 0.01),
        ),
      ),
    );
  }

  bool _validateAddress() {
    final isValid = _streetAddress.isNotEmpty &&
        _state.isNotEmpty &&
        _city.isNotEmpty &&
        _zipCode.isNotEmpty;
    setState(() {
      _showAddressError = !isValid;
    });
    return isValid;
  }

  bool _validateDropoffOption() {
    final isValid = _selectedDropoffOption != null;
    setState(() {
      _showDropoffError = !isValid;
    });
    return isValid;
  }

  void _saveDeliveryDetails() {
    if (!_validateAddress()) return;
    if (!_validateDropoffOption()) return;

    setState(() => _isPlacingOrder = true);

    final Map<String, dynamic> requestData = {
      "catering_id": widget.cateringId,
      "drop_off_option_id": _selectedDropoffOption!.id,
      "drop_off_instructions": _dropOffInstructions ?? '',
    };

    // Add address fields only if address was edited
    if (_addressWasEdited) {
      requestData.addAll({
        "address": _streetAddress,
        "state": _state,
        "city": _city,
        "zip_code": _zipCode,
      });
    }

    context.read<CateringBloc>().add(
          CheckoutCateringRequest(requestData),
        );
  }

  void _navigateToAddressEdit() async {
    final result = await Navigator.of(context).push<Map<String, String>>(
      MaterialPageRoute(
        builder: (context) => AddressEditPage(
          initialAddress: _streetAddress,
          initialState: _state,
          initialCity: _city,
          initialZipCode: _zipCode,
        ),
      ),
    );

    if (result != null) {
      setState(() {
        _streetAddress = result['address'] ?? '';
        _state = result['state'] ?? '';
        _city = result['city'] ?? '';
        _zipCode = result['zipCode'] ?? '';
        _showAddressError = false;
        _addressWasEdited = true; // Set to true when address is edited
      });
    }
  }

  void _updateAddressFields(ViewCateringRequestData data) {
    if (data.catering != null) {
      setState(() {
        _streetAddress = data.catering!.address ?? '';
        _state = data.catering!.state ?? '';
        _city = data.catering!.city ?? '';
        _zipCode = data.catering!.zipCode ?? '';
        _date = data.catering!.date ?? '';
      });
    }
    if (data.catering?.timeSlot != null) {
      setState(() {
        _startTime = data.catering!.timeSlot!.startTime ?? '';
        _endTime = data.catering!.timeSlot!.endTime ?? '';
      });
    }
  }

  String get _formattedAddress {
    if (_streetAddress.isEmpty) return 'Add delivery address';
    return _streetAddress;
  }

  String _formatTime(String time) {
    try {
      // Parse thetwentyFour-hour time string
      final timeParts = time.split(':');
      if (timeParts.length < 2) return time;

      int hour = int.parse(timeParts[0]);
      int minute = int.parse(timeParts[1]);

      // Determine AM/PM
      String period = hour >= 12 ? 'PM' : 'AM';

      // Convert to 12-hour format
      if (hour > 12) {
        hour -= 12;
      } else if (hour == 0) {
        hour = 12;
      }

      return '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')} $period';
    } catch (e) {
      return time;
    }
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    return MultiBlocListener(
      listeners: [
        BlocListener<CateringBloc, CateringState>(
          listener: (context, state) {
            if (state is CheckoutCateringRequestSuccess) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message),
                  backgroundColor: const Color(0xFF007A4D),
                ),
              );
              Navigator.of(context).pushAndRemoveUntil(
                MaterialPageRoute(
                  builder: (context) => const MainNavigationScreen(),
                ),
                (route) => false,
              );
            } else if (state is CheckoutCateringRequestFailed) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message),
                  backgroundColor: Colors.red,
                ),
              );
              setState(() => _isPlacingOrder = false);
            } else if (state is ViewCateringRequestSuccess) {
              _updateAddressFields(Initializer.viewCateringRequestModel.data!);
            }
          },
        ),
      ],
      child: Scaffold(
        backgroundColor: const Color(0xFFF6F3EC),
        appBar: AppBar(
          backgroundColor: const Color(0xFFF6F3EC),
          scrolledUnderElevation: 0,
          surfaceTintColor: Colors.transparent,
          elevation: 0,
          automaticallyImplyLeading: false,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: Color(0xFF1F2122)),
            onPressed: () => Navigator.of(context).pop(),
          ),
          title: Text(
            'Checkout',
            style: TextStyle(
              color: Color(0xFF1F2122),
              fontSize: eighteen,
              fontWeight: FontWeight.w600,
              fontFamily: 'Inter',
              height: 1.24,
            ),
          ),
        ),
        body: SingleChildScrollView(
          padding:
              EdgeInsets.only(left: sixteen, right: sixteen, bottom: sixteen),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 2,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                padding: EdgeInsets.only(
                    left: twenty, right: twenty, top: sixteen, bottom: twenty),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Delivery Details',
                      style: TextStyle(
                        fontFamily: 'Inter',
                        fontSize: twenty,
                        fontWeight: FontWeight.w600,
                        height: 1.24,
                        color: Color(0xFF1F2122),
                      ),
                    ),
                    SizedBox(height: twentyFour),

                    // Address Section
                    BlocBuilder<CateringBloc, CateringState>(
                      buildWhen: (previous, current) =>
                          current is ViewCateringRequestLoading ||
                          current is ViewCateringRequestSuccess ||
                          current is ViewCateringRequestFailed,
                      builder: (context, state) {
                        if (state is ViewCateringRequestLoading) {
                          return _buildLoadingShimmer(size);
                        }

                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Address',
                              style: TextStyle(
                                fontFamily: 'Inter',
                                fontSize: forteen,
                                fontWeight: FontWeight.w500,
                                color: Color(0xFF1F2122),
                              ),
                            ),
                            SizedBox(height: sixteen / 2),
                            InkWell(
                              // onTap: _navigateToAddressEdit, // Edit commented as per client reqr.
                              borderRadius: BorderRadius.circular(sixteen / 2),
                              child: Container(
                                padding:
                                    EdgeInsets.symmetric(vertical: twelve / 2),
                                child: Row(
                                  children: [
                                    Icon(
                                      Icons.location_on_outlined,
                                      color: Color(0xFF66696D),
                                      size: twenty,
                                    ),
                                    SizedBox(width: twelve),
                                    Expanded(
                                      child: Text(
                                        _formattedAddress,
                                        style: TextStyle(
                                          fontFamily: 'Inter',
                                          fontSize: forteen,
                                          fontWeight: FontWeight.w500,
                                          color: _streetAddress.isEmpty
                                              ? const Color(0xFF66696D)
                                              : const Color(0xFF1F2122),
                                        ),
                                      ),
                                    ),
                                    // Text(
                                    //   'Edit',
                                    //   style: TextStyle(
                                    //     fontFamily: 'Inter',
                                    //     fontSize: twelve,
                                    //     fontWeight: FontWeight.w400,
                                    //     color: Color.fromARGB(255, 15, 15, 15),
                                    //     decoration: TextDecoration.underline,
                                    //   ),
                                    // ),
                                  ],
                                ),
                              ),
                            ),
                            if (_showAddressError)
                              Padding(
                                padding: EdgeInsets.only(
                                    top: sixteen / 4, left: ten + ten + twelve),
                                child: Text(
                                  'Please add a delivery address',
                                  style: TextStyle(
                                    fontFamily: 'Inter',
                                    fontSize: twelve,
                                    color: Colors.red,
                                  ),
                                ),
                              ),
                          ],
                        );
                      },
                    ),

                    SizedBox(height: ten),

                    // Delivery Time Section
                    Text(
                      'Delivery',
                      style: TextStyle(
                        fontFamily: 'Inter',
                        fontSize: forteen,
                        fontWeight: FontWeight.w500,
                        color: const Color(0xFF1F2122),
                      ),
                    ),
                    SizedBox(height: forteen),
                    Row(
                      children: [
                        Icon(
                          Icons.schedule_outlined,
                          color: Color(0xFF66696D),
                          size: twenty,
                        ),
                        SizedBox(width: twelve),
                        Text(
                          '$_date, ${_formatTime(_startTime)} - ${_formatTime(_endTime)}',
                          style: TextStyle(
                            fontFamily: 'Inter',
                            fontSize: forteen,
                            fontWeight: FontWeight.w500,
                            color: Color(0xFF1F2122),
                          ),
                        ),
                      ],
                    ),

                    SizedBox(height: twentyFour),
                    Divider(
                      color: const Color(0xFFE1E3E6),
                      thickness: 1,
                      height: sixteen /
                          16, // Optional: avoids extra space above/below
                    ),
                    SizedBox(height: twentyFour),
                    // Drop-off Options Section
                    BlocConsumer<MealplanBloc, MealPlanState>(
                      listener: (context, state) {
                        if (state is ListDropoffOptionSuccess) {
                          final dropoffOptionsModel =
                              state.data as DropoffOptionsModel;
                          setState(() {
                            _dropoffOptions =
                                dropoffOptionsModel.data?.data ?? [];
                            _dropoffErrorMessage = null;
                          });
                        } else if (state is ListDropoffOptionFailed) {
                          setState(() {
                            _dropoffErrorMessage = state.message;
                            _dropoffOptions = [];
                          });
                        }
                      },
                      builder: (context, state) {
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Drop-Off Options',
                              style: TextStyle(
                                fontFamily: 'Inter',
                                fontSize: forteen,
                                fontWeight: FontWeight.w500,
                                color: Color(0xFF1F2122),
                              ),
                            ),
                            SizedBox(height: sixteen / 2),
                            if (state is ListDropoffOptionLoading)
                              _buildLoadingShimmer(size)
                            else if (_dropoffErrorMessage != null)
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    _dropoffErrorMessage!,
                                    style: TextStyle(
                                      fontFamily: 'Inter',
                                      fontSize: forteen,
                                      color: Colors.red,
                                    ),
                                  ),
                                  TextButton(
                                    onPressed: () {
                                      context
                                          .read<MealplanBloc>()
                                          .add(ListDropoffOptionEvent());
                                      setState(
                                          () => _dropoffErrorMessage = null);
                                    },
                                    child: Text(
                                      'Retry',
                                      style: TextStyle(
                                        fontFamily: 'Inter',
                                        fontSize: forteen,
                                        color: Color(0xFF007A4D),
                                        decoration: TextDecoration.underline,
                                      ),
                                    ),
                                  ),
                                ],
                              )
                            else if (_dropoffOptions.isNotEmpty)
                              Container(
                                decoration: BoxDecoration(
                                  border: Border.all(
                                    color: _showDropoffError
                                        ? Colors.red
                                        : const Color(0xFFE1E3E6),
                                  ),
                                  borderRadius: BorderRadius.circular(ten * 3),
                                ),
                                child:
                                    DropdownButtonFormField<DropoffOptionItem>(
                                  isDense: true,
                                  value: _selectedDropoffOption,
                                  decoration: InputDecoration(
                                    contentPadding: EdgeInsets.symmetric(
                                      horizontal: sixteen,
                                      vertical: 0,
                                    ),
                                    border: InputBorder.none,
                                    enabledBorder: InputBorder.none,
                                    focusedBorder: InputBorder.none,
                                    errorBorder: InputBorder.none,
                                    focusedErrorBorder: InputBorder.none,
                                  ),
                                  items: _dropoffOptions.map((option) {
                                    return DropdownMenuItem<DropoffOptionItem>(
                                      value: option,
                                      child: Text(
                                        option.name ?? '',
                                        style: TextStyle(
                                          fontFamily: 'Inter',
                                          fontSize: forteen,
                                          color: Color(0xFF1F2122),
                                        ),
                                      ),
                                    );
                                  }).toList(),
                                  onChanged: (value) {
                                    setState(() {
                                      _selectedDropoffOption = value;
                                      _showDropoffError = false;
                                    });
                                  },
                                  hint: Text(
                                    'Select drop-off option',
                                    style: TextStyle(
                                      fontFamily: 'Inter',
                                      fontSize: sixteen,
                                      fontWeight: FontWeight.w400,
                                      color: Color(0xFF66696D),
                                    ),
                                  ),
                                  icon: const Icon(
                                    Icons.keyboard_arrow_down,
                                    color: Color(0xFF66696D),
                                  ),
                                ),
                              )
                            else
                              Text(
                                'No drop-off options available',
                                style: TextStyle(
                                  fontFamily: 'Inter',
                                  fontSize: sixteen,
                                  fontWeight: FontWeight.w400,
                                  color: Color(0xFF66696D),
                                ),
                              ),
                            if (_showDropoffError)
                              Padding(
                                padding: EdgeInsets.only(top: 4),
                                child: Text(
                                  'Please select a drop-off option',
                                  style: TextStyle(
                                    fontFamily: 'Inter',
                                    fontSize: forteen,
                                    fontWeight: FontWeight.w400,
                                    color: Colors.red,
                                  ),
                                ),
                              ),
                          ],
                        );
                      },
                    ),

                    SizedBox(height: twentyFour),

                    // Drop-Off Instructions Section
                    Text(
                      'Drop-Off Instructions',
                      style: TextStyle(
                        fontFamily: 'Inter',
                        fontSize: forteen,
                        fontWeight: FontWeight.w500,
                        color: Color(0xFF1F2122),
                      ),
                    ),
                    SizedBox(height: sixteen / 2),
                    TextField(
                      controller: _instructionsController,
                      onChanged: (value) {
                        _dropOffInstructions = value;
                      },
                      decoration: InputDecoration(
                        contentPadding: EdgeInsets.all(sixteen),
                        hintText: 'Example: Doorbell is broken, please knock',
                        hintStyle: TextStyle(
                          fontFamily: 'Inter',
                          fontWeight: FontWeight.w400,
                          fontSize: sixteen,
                          color: Color(0xFF66696D),
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(sixteen / 2),
                          borderSide:
                              const BorderSide(color: Color(0xFFE1E3E6)),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(sixteen / 2),
                          borderSide:
                              const BorderSide(color: Color(0xFFE1E3E6)),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(sixteen / 2),
                          borderSide: const BorderSide(
                              color: Color(0xFF1F2122), width: 1.5),
                        ),
                      ),
                      minLines: 5,
                      maxLines: 7,
                      cursorColor: const Color(0xFF1F2122),
                      style: TextStyle(
                        fontFamily: 'Inter',
                        fontWeight: FontWeight.w400,
                        fontSize: sixteen,
                        color: Color(0xFF1F2122),
                      ),
                    ),
                    SizedBox(
                      height: eighteen,
                    ),
                    Divider(
                      color: const Color(0xFFE1E3E6),
                      thickness: 1,
                      height: sixteen /
                          16, // Optional: avoids extra space above/below
                    ),
                    //SizedBox(height: ten/2,),
                  ],
                ),
              ),
              SizedBox(height: twentyFour),
            ],
          ),
        ),
        bottomNavigationBar: BlocBuilder<CateringBloc, CateringState>(
          buildWhen: (previous, current) =>
              current is CheckoutCateringRequestLoading ||
              current is CheckoutCateringRequestSuccess ||
              current is CheckoutCateringRequestFailed,
          builder: (context, state) {
            final isLoading = state is CheckoutCateringRequestLoading;

            return Container(
              color: Colors.white,
              padding: EdgeInsets.only(
                  top: sixteen,
                  bottom: twentyFour + twentyFour,
                  left: sixteen,
                  right: sixteen),
              child: SizedBox(
                width: double.infinity,
                height: 4 * ten + ten + ten,
                child: ElevatedButton(
                  onPressed: isLoading ? null : _saveDeliveryDetails,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF1F2122),
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(26),
                    ),
                    elevation: 0,
                  ),
                  child: isLoading
                      ? SizedBox(
                          width: twentyFour,
                          height: twentyFour,
                          child: CircularProgressIndicator(
                            color: Colors.white,
                            strokeWidth: 2,
                          ),
                        )
                      : Text(
                          'Place Order',
                          style: TextStyle(
                            fontFamily: 'Inter',
                            fontSize: sixteen,
                            fontWeight: FontWeight.w400,
                            height: 1.0,
                            letterSpacing: 0.32,
                            color: Colors.white,
                          ),
                        ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
