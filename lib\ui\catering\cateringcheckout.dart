import 'package:db_eats/bloc/catering_bloc.dart';
import 'package:db_eats/bloc/mealplan_bloc.dart';
import 'package:db_eats/common/initializer.dart';
import 'package:db_eats/data/models/catering/cateringordersummarymodel.dart';
import 'package:db_eats/data/models/catering/viewcateringrequestsmodel.dart';
import 'package:db_eats/data/models/meal_plan/deliverytimemodel.dart';
import 'package:db_eats/data/models/meal_plan/dropoffoptionmodel.dart';
import 'package:db_eats/ui/catering/editaddress_checkout.dart';
import 'package:db_eats/ui/mainnavigationpage.dart';
import 'package:db_eats/utils/dotteddivider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shimmer/shimmer.dart';

class CateringCheckoutPage extends StatefulWidget {
  final int cateringId;

  const CateringCheckoutPage({super.key, required this.cateringId});

  @override
  State<CateringCheckoutPage> createState() => _CateringCheckoutPageState();
}

class _CateringCheckoutPageState extends State<CateringCheckoutPage> {
  DropoffOptionItem? _selectedDropoffOption;
  List<DropoffOptionItem> _dropoffOptions = [];
  String? _dropOffInstructions;
  bool _showDropoffError = false;
  bool _isPlacingOrder = false;
  late TextEditingController _instructionsController;

  // Delivery time selection
  DeliveryTimeItem? _selectedDeliveryTime;
  List<DeliveryTimeItem> _deliveryTimeOptions = [];
  bool _showDeliveryTimeError = false;

  // Wallet functionality
  bool _useWalletCredits = false;
  CateringOrderData? _orderSummaryData;

  // Address fields
  String _streetAddress = '';
  String _state = '';
  String _city = '';
  String _zipCode = '';
  bool _showAddressError = false;
  String? _dropoffErrorMessage;

  String _date = '';
  String _startTime = '';
  String _endTime = '';

  bool _addressWasEdited = false;

  @override
  void initState() {
    super.initState();
    _instructionsController = TextEditingController();
    context.read<MealplanBloc>().add(ListDropoffOptionEvent());
    context.read<MealplanBloc>().add(ListDeliveryTimeEvent());
    context.read<CateringBloc>().add(ViewCateringRequest(widget.cateringId));
    // Get initial order summary
    context.read<CateringBloc>().add(ViewCteringSummary({
          "catering_id": widget.cateringId,
        }));
  }

  late double ten;
  late double twelve;
  late double forteen;
  late double sixteen;
  late double eighteen;
  late double twenty;
  late double twentyFour;
  late double screenWidth;
  late double screenHeight;
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    screenWidth = MediaQuery.of(context).size.width;
    screenHeight = MediaQuery.of(context).size.height;

    ten = screenWidth * 0.02545;
    twelve = screenWidth * 0.03054;
    forteen = screenWidth * 0.035;
    sixteen = screenWidth * 0.04073;
    eighteen = screenWidth * 0.04582;
    twenty = screenWidth * 0.05091;
    twentyFour = screenWidth * 0.06109;
  }

  @override
  void dispose() {
    _instructionsController.dispose();
    super.dispose();
  }

  Widget _buildLoadingShimmer(Size size) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        height: size.height * 0.025,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(size.width * 0.01),
        ),
      ),
    );
  }

  bool _validateAddress() {
    final isValid = _streetAddress.isNotEmpty &&
        _state.isNotEmpty &&
        _city.isNotEmpty &&
        _zipCode.isNotEmpty;
    setState(() {
      _showAddressError = !isValid;
    });
    return isValid;
  }

  bool _validateDropoffOption() {
    final isValid = _selectedDropoffOption != null;
    setState(() {
      _showDropoffError = !isValid;
    });
    return isValid;
  }

  bool _validateDeliveryTime() {
    final isValid = _selectedDeliveryTime != null;
    setState(() {
      _showDeliveryTimeError = !isValid;
    });
    return isValid;
  }

  void _saveDeliveryDetails() {
    if (!_validateAddress()) return;
    if (!_validateDropoffOption()) return;
    if (!_validateDeliveryTime()) return;

    setState(() => _isPlacingOrder = true);

    final Map<String, dynamic> requestData = {
      "catering_id": widget.cateringId,
      "drop_off_option_id": _selectedDropoffOption!.id,
      "drop_off_instructions": _dropOffInstructions ?? '',
      "delivery_time_id": _selectedDeliveryTime!.id,
    };

    // Add wallet credits if enabled
    if (_useWalletCredits) {
      requestData["use_wallet_credits"] = true;
    }

    // Add address fields only if address was edited
    if (_addressWasEdited) {
      requestData.addAll({
        "address": _streetAddress,
        "state": _state,
        "city": _city,
        "zip_code": _zipCode,
      });
    }

    context.read<CateringBloc>().add(
          CheckoutCateringRequest(requestData),
        );
  }

  void _navigateToAddressEdit() async {
    final result = await Navigator.of(context).push<Map<String, String>>(
      MaterialPageRoute(
        builder: (context) => AddressEditPage(
          initialAddress: _streetAddress,
          initialState: _state,
          initialCity: _city,
          initialZipCode: _zipCode,
        ),
      ),
    );

    if (result != null) {
      setState(() {
        _streetAddress = result['address'] ?? '';
        _state = result['state'] ?? '';
        _city = result['city'] ?? '';
        _zipCode = result['zipCode'] ?? '';
        _showAddressError = false;
        _addressWasEdited = true; // Set to true when address is edited
      });
    }
  }

  void _updateAddressFields(ViewCateringRequestData data) {
    if (data.catering != null) {
      setState(() {
        _streetAddress = data.catering!.address ?? '';
        _state = data.catering!.state ?? '';
        _city = data.catering!.city ?? '';
        _zipCode = data.catering!.zipCode ?? '';
        _date = data.catering!.date ?? '';
      });
    }
    if (data.catering?.timeSlot != null) {
      setState(() {
        _startTime = data.catering!.timeSlot!.startTime ?? '';
        _endTime = data.catering!.timeSlot!.endTime ?? '';
      });
    }
  }

  String get _formattedAddress {
    if (_streetAddress.isEmpty) return 'Add delivery address';
    return _streetAddress;
  }

  String _formatTime(String time) {
    try {
      // Parse thetwentyFour-hour time string
      final timeParts = time.split(':');
      if (timeParts.length < 2) return time;

      int hour = int.parse(timeParts[0]);
      int minute = int.parse(timeParts[1]);

      // Determine AM/PM
      String period = hour >= 12 ? 'PM' : 'AM';

      // Convert to 12-hour format
      if (hour > 12) {
        hour -= 12;
      } else if (hour == 0) {
        hour = 12;
      }

      return '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')} $period';
    } catch (e) {
      return time;
    }
  }

  Widget _buildOrderSummaryRow(String label, String value,
      {bool isDiscount = false, bool isTotal = false}) {
    return Padding(
      padding: EdgeInsets.only(bottom: ten),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontFamily: 'Inter',
              fontSize: isTotal ? sixteen : forteen,
              fontWeight: isTotal ? FontWeight.w600 : FontWeight.w400,
              color: Color(0xFF1F2122),
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontFamily: 'Inter',
              fontSize: isTotal ? sixteen : forteen,
              fontWeight: isTotal ? FontWeight.w600 : FontWeight.w500,
              color: isDiscount ? const Color(0xFF007A4D) : Color(0xFF1F2122),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWalletToggle() {
    return GestureDetector(
      onTap: () {
        // Check if wallet balance is 0 or null and user is trying to enable
        if (!_useWalletCredits &&
            (_orderSummaryData?.walletBalance == null ||
                _orderSummaryData!.walletBalance! <= 0)) {
          // Show toast message
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text("No available credits in your Dabba wallet"),
              duration: Duration(seconds: 2),
              backgroundColor: Color.fromARGB(255, 230, 74, 53),
              behavior: SnackBarBehavior.floating,
              margin: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(8)),
              ),
            ),
          );
          return; // Don't proceed with the toggle
        }

        setState(() {
          _useWalletCredits = !_useWalletCredits;
        });

        // Call ViewCteringSummary with use_wallet_credits to refresh order summary
        Map<String, dynamic> requestData = {
          "catering_id": widget.cateringId,
          "use_wallet_credits": _useWalletCredits,
        };

        // Include delivery_time_id if a delivery time is selected
        if (_selectedDeliveryTime != null) {
          requestData["delivery_time_id"] =
              _selectedDeliveryTime!.id.toString();
        }

        context.read<CateringBloc>().add(ViewCteringSummary(requestData));
      },
      child: Container(
        width: 44,
        height: 24,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: _useWalletCredits
              ? const Color(0xFF007A4D)
              : const Color(0xFFE1E3E6),
        ),
        child: AnimatedAlign(
          duration: const Duration(milliseconds: 200),
          alignment:
              _useWalletCredits ? Alignment.centerRight : Alignment.centerLeft,
          child: Container(
            width: 20,
            height: 20,
            margin: const EdgeInsets.all(2),
            decoration: const BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildOrderTotalSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Order Total',
              style: TextStyle(
                fontFamily: 'Inter',
                fontSize: sixteen,
                fontWeight: FontWeight.w500,
                color: const Color(0xFF1F2122),
              ),
            ),
          ],
        ),
        SizedBox(height: ten),
        Column(
          children: [
            _buildOrderSummaryRow('Subtotal',
                '\$${(_orderSummaryData!.subtotal ?? 0.0).toStringAsFixed(2)}'),
            if (_orderSummaryData!.deliveryFee != null &&
                _orderSummaryData!.deliveryFee! > 0)
              _buildOrderSummaryRow('Delivery Fee',
                  '\$${_orderSummaryData!.deliveryFee!.toStringAsFixed(2)}'),
            if (_orderSummaryData!.serviceFee != null &&
                _orderSummaryData!.serviceFee! > 0)
              _buildOrderSummaryRow('Service Fee',
                  '\$${_orderSummaryData!.serviceFee!.toStringAsFixed(2)}'),
            if (_orderSummaryData!.taxesAndFees != null &&
                _orderSummaryData!.taxesAndFees! > 0)
              _buildOrderSummaryRow('Taxes & Fees',
                  '\$${_orderSummaryData!.taxesAndFees!.toStringAsFixed(2)}'),
            if (_orderSummaryData!.discount != null &&
                _orderSummaryData!.discount! > 0)
              _buildOrderSummaryRow('Discount',
                  '-\$${_orderSummaryData!.discount!.toStringAsFixed(2)}',
                  isDiscount: true),
            if (_useWalletCredits &&
                _orderSummaryData!.walletCredits != null &&
                _orderSummaryData!.walletCredits! > 0)
              _buildOrderSummaryRow('Wallet Credits',
                  '-\$${((_orderSummaryData!.walletCredits!) / 100).toStringAsFixed(2)}',
                  isDiscount: true),
            DottedDivider(),
            _buildOrderSummaryRow(
              'Total',
              '\$${(_orderSummaryData!.total ?? 0.0).toStringAsFixed(2)}',
              isTotal: true,
            ),
          ],
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    return MultiBlocListener(
      listeners: [
        BlocListener<CateringBloc, CateringState>(
          listener: (context, state) {
            if (state is CheckoutCateringRequestSuccess) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message),
                  backgroundColor: const Color(0xFF007A4D),
                ),
              );
              Navigator.of(context).pushAndRemoveUntil(
                MaterialPageRoute(
                  builder: (context) => const MainNavigationScreen(),
                ),
                (route) => false,
              );
            } else if (state is CheckoutCateringRequestFailed) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message),
                  backgroundColor: Colors.red,
                ),
              );
              setState(() => _isPlacingOrder = false);
            } else if (state is ViewCateringRequestSuccess) {
              _updateAddressFields(Initializer.viewCateringRequestModel.data!);
            } else if (state is ViewCteringSummarySuccess) {
              setState(() {
                _orderSummaryData = state.data as CateringOrderData?;
              });
            }
          },
        ),
        BlocListener<MealplanBloc, MealPlanState>(
          listener: (context, state) {
            if (state is ListDeliveryTimeSuccess) {
              final deliveryTimeModel = state.data as DeliveryTimeModel;
              setState(() {
                _deliveryTimeOptions = deliveryTimeModel.data?.data ?? [];
              });
            }
          },
        ),
      ],
      child: Scaffold(
        backgroundColor: const Color(0xFFF6F3EC),
        appBar: AppBar(
          backgroundColor: const Color(0xFFF6F3EC),
          scrolledUnderElevation: 0,
          surfaceTintColor: Colors.transparent,
          elevation: 0,
          automaticallyImplyLeading: false,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: Color(0xFF1F2122)),
            onPressed: () => Navigator.of(context).pop(),
          ),
          title: Text(
            'Checkout',
            style: TextStyle(
              color: Color(0xFF1F2122),
              fontSize: eighteen,
              fontWeight: FontWeight.w600,
              fontFamily: 'Inter',
              height: 1.24,
            ),
          ),
        ),
        body: SingleChildScrollView(
          padding:
              EdgeInsets.only(left: sixteen, right: sixteen, bottom: sixteen),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 2,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                padding: EdgeInsets.only(
                    left: twenty, right: twenty, top: sixteen, bottom: twenty),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Delivery Details',
                      style: TextStyle(
                        fontFamily: 'Inter',
                        fontSize: twenty,
                        fontWeight: FontWeight.w600,
                        height: 1.24,
                        color: Color(0xFF1F2122),
                      ),
                    ),
                    SizedBox(height: twentyFour),

                    // Address Section
                    BlocBuilder<CateringBloc, CateringState>(
                      buildWhen: (previous, current) =>
                          current is ViewCateringRequestLoading ||
                          current is ViewCateringRequestSuccess ||
                          current is ViewCateringRequestFailed,
                      builder: (context, state) {
                        if (state is ViewCateringRequestLoading) {
                          return _buildLoadingShimmer(size);
                        }

                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Address',
                              style: TextStyle(
                                fontFamily: 'Inter',
                                fontSize: forteen,
                                fontWeight: FontWeight.w500,
                                color: Color(0xFF1F2122),
                              ),
                            ),
                            SizedBox(height: sixteen / 2),
                            InkWell(
                              // onTap: _navigateToAddressEdit, // Edit commented as per client reqr.
                              borderRadius: BorderRadius.circular(sixteen / 2),
                              child: Container(
                                padding:
                                    EdgeInsets.symmetric(vertical: twelve / 2),
                                child: Row(
                                  children: [
                                    Icon(
                                      Icons.location_on_outlined,
                                      color: Color(0xFF66696D),
                                      size: twenty,
                                    ),
                                    SizedBox(width: twelve),
                                    Expanded(
                                      child: Text(
                                        _formattedAddress,
                                        style: TextStyle(
                                          fontFamily: 'Inter',
                                          fontSize: forteen,
                                          fontWeight: FontWeight.w500,
                                          color: _streetAddress.isEmpty
                                              ? const Color(0xFF66696D)
                                              : const Color(0xFF1F2122),
                                        ),
                                      ),
                                    ),
                                    // Text(
                                    //   'Edit',
                                    //   style: TextStyle(
                                    //     fontFamily: 'Inter',
                                    //     fontSize: twelve,
                                    //     fontWeight: FontWeight.w400,
                                    //     color: Color.fromARGB(255, 15, 15, 15),
                                    //     decoration: TextDecoration.underline,
                                    //   ),
                                    // ),
                                  ],
                                ),
                              ),
                            ),
                            if (_showAddressError)
                              Padding(
                                padding: EdgeInsets.only(
                                    top: sixteen / 4, left: ten + ten + twelve),
                                child: Text(
                                  'Please add a delivery address',
                                  style: TextStyle(
                                    fontFamily: 'Inter',
                                    fontSize: twelve,
                                    color: Colors.red,
                                  ),
                                ),
                              ),
                          ],
                        );
                      },
                    ),

                    SizedBox(height: ten),

                    // Delivery Time Section
                    Text(
                      'Delivery',
                      style: TextStyle(
                        fontFamily: 'Inter',
                        fontSize: forteen,
                        fontWeight: FontWeight.w500,
                        color: const Color(0xFF1F2122),
                      ),
                    ),
                    SizedBox(height: forteen),
                    Row(
                      children: [
                        Icon(
                          Icons.schedule_outlined,
                          color: Color(0xFF66696D),
                          size: twenty,
                        ),
                        SizedBox(width: twelve),
                        Text(
                          '$_date, ${_formatTime(_startTime)} - ${_formatTime(_endTime)}',
                          style: TextStyle(
                            fontFamily: 'Inter',
                            fontSize: forteen,
                            fontWeight: FontWeight.w500,
                            color: Color(0xFF1F2122),
                          ),
                        ),
                      ],
                    ),

                    SizedBox(height: twentyFour),
                    Divider(
                      color: const Color(0xFFE1E3E6),
                      thickness: 1,
                      height: sixteen /
                          16, // Optional: avoids extra space above/below
                    ),
                    SizedBox(height: twentyFour),
                    // Delivery Time Selection Section
                    Text(
                      'Delivery Time',
                      style: TextStyle(
                        fontFamily: 'Inter',
                        fontSize: forteen,
                        fontWeight: FontWeight.w500,
                        color: Color(0xFF1F2122),
                      ),
                    ),
                    SizedBox(height: sixteen / 2),
                    if (_deliveryTimeOptions.isNotEmpty)
                      Column(
                        children: _deliveryTimeOptions
                            .map((option) => Padding(
                                  padding: EdgeInsets.only(bottom: ten),
                                  child: InkWell(
                                    onTap: () {
                                      setState(() {
                                        _selectedDeliveryTime = option;
                                        _showDeliveryTimeError = false;
                                      });

                                      // Call ViewCteringSummary with delivery_time_id to refresh order summary
                                      Map<String, dynamic> requestData = {
                                        "catering_id": widget.cateringId,
                                        "delivery_time_id":
                                            option.id.toString(),
                                      };

                                      // Include use_wallet_credits if wallet credits are enabled
                                      if (_useWalletCredits) {
                                        requestData["use_wallet_credits"] =
                                            true;
                                      }

                                      context
                                          .read<CateringBloc>()
                                          .add(ViewCteringSummary(requestData));
                                    },
                                    child: Container(
                                      padding: EdgeInsets.all(sixteen),
                                      decoration: BoxDecoration(
                                        border: Border.all(
                                          color: _selectedDeliveryTime?.id ==
                                                  option.id
                                              ? const Color.fromARGB(
                                                  255, 60, 61, 61)
                                              : const Color(0xFFE1E3E6),
                                          width: _selectedDeliveryTime?.id ==
                                                  option.id
                                              ? 2
                                              : 1,
                                        ),
                                        borderRadius:
                                            BorderRadius.circular(ten),
                                        color: _selectedDeliveryTime?.id ==
                                                option.id
                                            ? const Color.fromARGB(
                                                    255, 180, 192, 187)
                                                .withOpacity(0.05)
                                            : Colors.white,
                                      ),
                                      child: Row(
                                        children: [
                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  option.name ?? '',
                                                  style: TextStyle(
                                                    fontFamily: 'Inter',
                                                    fontSize: forteen,
                                                    fontWeight: FontWeight.w600,
                                                    color: Color(0xFF1F2122),
                                                  ),
                                                ),
                                                if (option.description !=
                                                        null &&
                                                    option.description!
                                                        .isNotEmpty)
                                                  Padding(
                                                    padding:
                                                        EdgeInsets.only(top: 4),
                                                    child: Text(
                                                      option.description!,
                                                      style: TextStyle(
                                                        fontFamily: 'Inter',
                                                        fontSize: twelve,
                                                        fontWeight:
                                                            FontWeight.w400,
                                                        color:
                                                            Color(0xFF66696D),
                                                      ),
                                                    ),
                                                  ),
                                              ],
                                            ),
                                          ),
                                          Text(
                                            '+\$${option.cost ?? '0.00'}',
                                            style: TextStyle(
                                              fontFamily: 'Inter',
                                              fontSize: forteen,
                                              fontWeight: FontWeight.w600,
                                              color: Color(0xFF1F2122),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ))
                            .toList(),
                      )
                    else
                      Text(
                        'No delivery time options available',
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontSize: forteen,
                          fontWeight: FontWeight.w400,
                          color: Color(0xFF66696D),
                        ),
                      ),
                    if (_showDeliveryTimeError)
                      Padding(
                        padding: EdgeInsets.only(top: 4),
                        child: Text(
                          'Please select a delivery time',
                          style: TextStyle(
                            fontFamily: 'Inter',
                            fontSize: forteen,
                            fontWeight: FontWeight.w400,
                            color: Colors.red,
                          ),
                        ),
                      ),

                    SizedBox(height: twentyFour),
                    Divider(
                      color: const Color(0xFFE1E3E6),
                      thickness: 1,
                      height: sixteen /
                          16, // Optional: avoids extra space above/below
                    ),
                    SizedBox(height: twentyFour),
                    // Drop-off Options Section
                    BlocConsumer<MealplanBloc, MealPlanState>(
                      listener: (context, state) {
                        if (state is ListDropoffOptionSuccess) {
                          final dropoffOptionsModel =
                              state.data as DropoffOptionsModel;
                          setState(() {
                            _dropoffOptions =
                                dropoffOptionsModel.data?.data ?? [];
                            _dropoffErrorMessage = null;
                          });
                        } else if (state is ListDropoffOptionFailed) {
                          setState(() {
                            _dropoffErrorMessage = state.message;
                            _dropoffOptions = [];
                          });
                        }
                      },
                      builder: (context, state) {
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Drop-Off Options',
                              style: TextStyle(
                                fontFamily: 'Inter',
                                fontSize: forteen,
                                fontWeight: FontWeight.w500,
                                color: Color(0xFF1F2122),
                              ),
                            ),
                            SizedBox(height: sixteen / 2),
                            if (state is ListDropoffOptionLoading)
                              _buildLoadingShimmer(size)
                            else if (_dropoffErrorMessage != null)
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    _dropoffErrorMessage!,
                                    style: TextStyle(
                                      fontFamily: 'Inter',
                                      fontSize: forteen,
                                      color: Colors.red,
                                    ),
                                  ),
                                  TextButton(
                                    onPressed: () {
                                      context
                                          .read<MealplanBloc>()
                                          .add(ListDropoffOptionEvent());
                                      setState(
                                          () => _dropoffErrorMessage = null);
                                    },
                                    child: Text(
                                      'Retry',
                                      style: TextStyle(
                                        fontFamily: 'Inter',
                                        fontSize: forteen,
                                        color: Color(0xFF007A4D),
                                        decoration: TextDecoration.underline,
                                      ),
                                    ),
                                  ),
                                ],
                              )
                            else if (_dropoffOptions.isNotEmpty)
                              Container(
                                decoration: BoxDecoration(
                                  border: Border.all(
                                    color: _showDropoffError
                                        ? Colors.red
                                        : const Color(0xFFE1E3E6),
                                  ),
                                  borderRadius: BorderRadius.circular(ten * 3),
                                ),
                                child:
                                    DropdownButtonFormField<DropoffOptionItem>(
                                  isDense: true,
                                  value: _selectedDropoffOption,
                                  decoration: InputDecoration(
                                    contentPadding: EdgeInsets.symmetric(
                                      horizontal: sixteen,
                                      vertical: 0,
                                    ),
                                    border: InputBorder.none,
                                    enabledBorder: InputBorder.none,
                                    focusedBorder: InputBorder.none,
                                    errorBorder: InputBorder.none,
                                    focusedErrorBorder: InputBorder.none,
                                  ),
                                  items: _dropoffOptions.map((option) {
                                    return DropdownMenuItem<DropoffOptionItem>(
                                      value: option,
                                      child: Text(
                                        option.name ?? '',
                                        style: TextStyle(
                                          fontFamily: 'Inter',
                                          fontSize: forteen,
                                          color: Color(0xFF1F2122),
                                        ),
                                      ),
                                    );
                                  }).toList(),
                                  onChanged: (value) {
                                    setState(() {
                                      _selectedDropoffOption = value;
                                      _showDropoffError = false;
                                    });
                                  },
                                  hint: Text(
                                    'Select drop-off option',
                                    style: TextStyle(
                                      fontFamily: 'Inter',
                                      fontSize: sixteen,
                                      fontWeight: FontWeight.w400,
                                      color: Color(0xFF66696D),
                                    ),
                                  ),
                                  icon: const Icon(
                                    Icons.keyboard_arrow_down,
                                    color: Color(0xFF66696D),
                                  ),
                                ),
                              )
                            else
                              Text(
                                'No drop-off options available',
                                style: TextStyle(
                                  fontFamily: 'Inter',
                                  fontSize: sixteen,
                                  fontWeight: FontWeight.w400,
                                  color: Color(0xFF66696D),
                                ),
                              ),
                            if (_showDropoffError)
                              Padding(
                                padding: EdgeInsets.only(top: 4),
                                child: Text(
                                  'Please select a drop-off option',
                                  style: TextStyle(
                                    fontFamily: 'Inter',
                                    fontSize: forteen,
                                    fontWeight: FontWeight.w400,
                                    color: Colors.red,
                                  ),
                                ),
                              ),
                          ],
                        );
                      },
                    ),

                    SizedBox(height: twentyFour),

                    // Drop-Off Instructions Section
                    Text(
                      'Drop-Off Instructions',
                      style: TextStyle(
                        fontFamily: 'Inter',
                        fontSize: forteen,
                        fontWeight: FontWeight.w500,
                        color: Color(0xFF1F2122),
                      ),
                    ),
                    SizedBox(height: sixteen / 2),
                    TextField(
                      controller: _instructionsController,
                      onChanged: (value) {
                        _dropOffInstructions = value;
                      },
                      decoration: InputDecoration(
                        contentPadding: EdgeInsets.all(sixteen),
                        hintText: 'Example: Doorbell is broken, please knock',
                        hintStyle: TextStyle(
                          fontFamily: 'Inter',
                          fontWeight: FontWeight.w400,
                          fontSize: sixteen,
                          color: Color(0xFF66696D),
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(sixteen / 2),
                          borderSide:
                              const BorderSide(color: Color(0xFFE1E3E6)),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(sixteen / 2),
                          borderSide:
                              const BorderSide(color: Color(0xFFE1E3E6)),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(sixteen / 2),
                          borderSide: const BorderSide(
                              color: Color(0xFF1F2122), width: 1.5),
                        ),
                      ),
                      minLines: 5,
                      maxLines: 7,
                      cursorColor: const Color(0xFF1F2122),
                      style: TextStyle(
                        fontFamily: 'Inter',
                        fontWeight: FontWeight.w400,
                        fontSize: sixteen,
                        color: Color(0xFF1F2122),
                      ),
                    ),
                    SizedBox(
                      height: eighteen,
                    ),
                    Divider(
                      color: const Color(0xFFE1E3E6),
                      thickness: 1,
                      height: sixteen /
                          16, // Optional: avoids extra space above/below
                    ),
                    SizedBox(height: twentyFour),

                    // Order Summary Section
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(10),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.05),
                            blurRadius: 2,
                            offset: const Offset(0, 1),
                          ),
                        ],
                      ),
                      padding: EdgeInsets.all(size.width * 0.04),
                      child: BlocBuilder<CateringBloc, CateringState>(
                        buildWhen: (previous, current) =>
                            current is ViewCteringSummaryLoading ||
                            current is ViewCteringSummarySuccess ||
                            current is ViewCteringSummaryFailed,
                        builder: (context, state) {
                          if (state is ViewCteringSummaryLoading) {
                            return Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: List.generate(
                                6,
                                (index) => Padding(
                                  padding: EdgeInsets.symmetric(
                                    vertical: size.height * 0.01,
                                  ),
                                  child: _buildLoadingShimmer(size),
                                ),
                              ),
                            );
                          }

                          if (_orderSummaryData != null) {
                            return Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Order Summary',
                                  style: TextStyle(
                                    fontFamily: 'Inter',
                                    fontSize: twenty,
                                    fontWeight: FontWeight.w500,
                                    color: const Color(0xFF1F2122),
                                  ),
                                ),
                                DottedDivider(),
                                SizedBox(height: size.height * 0.01),

                                // Order Details Section
                                if (_orderSummaryData!.cateringItems != null &&
                                    _orderSummaryData!
                                        .cateringItems!.isNotEmpty)
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(
                                            'Order Details (${_orderSummaryData!.cateringItems!.length} Items)',
                                            style: TextStyle(
                                              fontSize: sixteen,
                                              fontWeight: FontWeight.w500,
                                              color: const Color(0xFF1F2122),
                                            ),
                                          ),
                                        ],
                                      ),
                                      ...(_orderSummaryData!.cateringItems!.map(
                                        (item) => Padding(
                                          padding: EdgeInsets.symmetric(
                                            vertical: size.height * 0.01,
                                          ),
                                          child: Row(
                                            children: [
                                              Container(
                                                padding: EdgeInsets.symmetric(
                                                  horizontal:
                                                      size.width * 0.025,
                                                  vertical: size.height * 0.005,
                                                ),
                                                decoration: BoxDecoration(
                                                  color:
                                                      const Color(0xFFE1E3E6),
                                                  borderRadius:
                                                      BorderRadius.circular(26),
                                                ),
                                                child: Text(
                                                  '${item.quantity ?? 0}x',
                                                  style: TextStyle(
                                                    fontSize: sixteen,
                                                    fontWeight: FontWeight.w500,
                                                    color:
                                                        const Color(0xFF1F2122),
                                                  ),
                                                ),
                                              ),
                                              SizedBox(
                                                  width: size.width * 0.03),
                                              Expanded(
                                                child: Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    Text(
                                                      item.dishName ?? '',
                                                      style: TextStyle(
                                                        fontSize: sixteen,
                                                        fontWeight:
                                                            FontWeight.w600,
                                                        color: const Color(
                                                            0xFF1F2122),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              Text(
                                                '\$${((item.totalPrice ?? 0) / 100).toStringAsFixed(2)}',
                                                style: TextStyle(
                                                  fontSize: sixteen,
                                                  fontWeight: FontWeight.w500,
                                                  color:
                                                      const Color(0xFF1F2122),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      )),
                                    ],
                                  ),
                                DottedDivider(),
                                SizedBox(height: size.height * 0.01),

                                // Promo Code Section
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Row(
                                      children: [
                                        Icon(
                                          Icons.percent,
                                          size: size.width * 0.04,
                                          color: const Color(0xFF1F2122),
                                        ),
                                        SizedBox(width: size.width * 0.01),
                                        Text(
                                          'Add promo code',
                                          style: TextStyle(
                                            fontFamily: 'Inter',
                                            fontSize: sixteen,
                                            fontWeight: FontWeight.w400,
                                            color: const Color(0xFF1F2122),
                                          ),
                                        ),
                                      ],
                                    ),
                                    TextButton(
                                      onPressed: () {},
                                      style: TextButton.styleFrom(
                                        minimumSize: Size.zero,
                                        padding: EdgeInsets.zero,
                                        tapTargetSize:
                                            MaterialTapTargetSize.shrinkWrap,
                                      ),
                                      child: Row(
                                        children: [
                                          Icon(
                                            Icons.add,
                                            size: size.width * 0.04,
                                            color: const Color(0xFF1F2122),
                                          ),
                                          SizedBox(width: size.width * 0.01),
                                          Column(
                                            children: [
                                              Text(
                                                'Add',
                                                style: TextStyle(
                                                  fontFamily: 'Inter',
                                                  fontWeight: FontWeight.w600,
                                                  fontSize: forteen,
                                                  height: 1.0,
                                                  letterSpacing: 0.24,
                                                  color:
                                                      const Color(0xFF414346),
                                                ),
                                              ),
                                              SizedBox(
                                                  height: size.height * 0.002),
                                              Container(
                                                height: 1,
                                                width: size.width * 0.05,
                                                color: const Color(0xFF1F2122),
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                                SizedBox(height: size.height * 0.025),

                                // Dabba Wallet Credits Section - Always show
                                Column(
                                  children: [
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Row(
                                          children: [
                                            Container(
                                              width: size.width * 0.06,
                                              height: size.width * 0.045,
                                              decoration: BoxDecoration(
                                                color: const Color(0xFF1F2122),
                                                borderRadius:
                                                    BorderRadius.circular(4),
                                              ),
                                              alignment: Alignment.center,
                                              child: Text(
                                                'DB',
                                                style: TextStyle(
                                                  fontFamily: 'Inter',
                                                  fontSize: size.width * 0.025,
                                                  fontWeight: FontWeight.bold,
                                                  color: Colors.white,
                                                ),
                                              ),
                                            ),
                                            SizedBox(width: size.width * 0.025),
                                            Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  'Dabba Wallet Credits',
                                                  style: TextStyle(
                                                    fontFamily: 'Inter',
                                                    fontSize: sixteen,
                                                    fontWeight: FontWeight.w400,
                                                    color:
                                                        const Color(0xFF1F2122),
                                                  ),
                                                ),
                                                Text(
                                                  '\$ ${((_orderSummaryData!.walletBalance ?? 0) / 100).toStringAsFixed(2)}',
                                                  style: TextStyle(
                                                    fontFamily: 'Inter',
                                                    fontSize: forteen,
                                                    fontWeight: FontWeight.w400,
                                                    color:
                                                        const Color(0xFF66696D),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                        _buildWalletToggle(),
                                      ],
                                    ),
                                    SizedBox(height: size.height * 0.01),
                                    DottedDivider(),
                                    SizedBox(height: size.height * 0.01),
                                  ],
                                ),

                                // Order Total Section
                                _buildOrderTotalSection(),
                                SizedBox(height: size.height * 0.025),
                                Text(
                                  "By placing this order, you agree to our terms and conditions.",
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    fontFamily: 'Inter',
                                    fontSize: forteen,
                                    fontWeight: FontWeight.w400,
                                    height: 1.43,
                                    color: const Color(0xFF66696D),
                                  ),
                                ),
                              ],
                            );
                          }

                          return SizedBox
                              .shrink(); // Return empty widget if no data
                        },
                      ),
                    ),
                    //SizedBox(height: ten/2,),
                  ],
                ),
              ),
              SizedBox(height: twentyFour),
            ],
          ),
        ),
        bottomNavigationBar: BlocBuilder<CateringBloc, CateringState>(
          buildWhen: (previous, current) =>
              current is CheckoutCateringRequestLoading ||
              current is CheckoutCateringRequestSuccess ||
              current is CheckoutCateringRequestFailed,
          builder: (context, state) {
            final isLoading = state is CheckoutCateringRequestLoading;

            return Container(
              color: Colors.white,
              padding: EdgeInsets.only(
                  top: sixteen,
                  bottom: twentyFour + twentyFour,
                  left: sixteen,
                  right: sixteen),
              child: SizedBox(
                width: double.infinity,
                height: 4 * ten + ten + ten,
                child: ElevatedButton(
                  onPressed: isLoading ? null : _saveDeliveryDetails,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF1F2122),
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(26),
                    ),
                    elevation: 0,
                  ),
                  child: isLoading
                      ? SizedBox(
                          width: twentyFour,
                          height: twentyFour,
                          child: CircularProgressIndicator(
                            color: Colors.white,
                            strokeWidth: 2,
                          ),
                        )
                      : Text(
                          'Place Order',
                          style: TextStyle(
                            fontFamily: 'Inter',
                            fontSize: sixteen,
                            fontWeight: FontWeight.w400,
                            height: 1.0,
                            letterSpacing: 0.32,
                            color: Colors.white,
                          ),
                        ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
