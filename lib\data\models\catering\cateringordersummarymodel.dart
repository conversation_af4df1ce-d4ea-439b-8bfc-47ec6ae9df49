class CateringOrderSummaryModel {
  bool? status;
  String? message;
  CateringOrderData? data;

  CateringOrderSummaryModel({this.status, this.message, this.data});

  CateringOrderSummaryModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    data =
        json['data'] != null ? CateringOrderData.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class CateringOrderData {
  CateringInfo? catering;
  List<CateringItem>? cateringItems;
  String? durationText;
  int? walletBalance;
  double? subtotal;
  double? discount;
  double? deliveryFee;
  int? serviceFeePercentage;
  double? serviceFee;
  int? walletCredits;
  int? taxPercentage;
  double? taxesAndFees;
  double? total;

  CateringOrderData({
    this.catering,
    this.cateringItems,
    this.durationText,
    this.walletBalance,
    this.subtotal,
    this.discount,
    this.deliveryFee,
    this.serviceFeePercentage,
    this.serviceFee,
    this.walletCredits,
    this.taxPercentage,
    this.taxesAndFees,
    this.total,
  });

  CateringOrderData.fromJson(Map<String, dynamic> json) {
    catering = json['catering'] != null
        ? CateringInfo.fromJson(json['catering'])
        : null;
    if (json['catering_items'] != null) {
      cateringItems = <CateringItem>[];
      json['catering_items'].forEach((v) {
        cateringItems!.add(CateringItem.fromJson(v));
      });
    }
    durationText = json['duration_text'];
    walletBalance = json['wallet_balance'];
    subtotal = json['subtotal']?.toDouble();
    discount = json['discount']?.toDouble();
    deliveryFee = json['delivery_fee']?.toDouble();
    serviceFeePercentage = json['service_fee_percentage'];
    serviceFee = json['service_fee']?.toDouble();
    walletCredits = json['wallet_credits'];
    taxPercentage = json['tax_percentage'];
    taxesAndFees = json['taxes_and_fees']?.toDouble();
    total = json['total']?.toDouble();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (catering != null) {
      data['catering'] = catering!.toJson();
    }
    if (cateringItems != null) {
      data['catering_items'] = cateringItems!.map((v) => v.toJson()).toList();
    }
    data['duration_text'] = durationText;
    data['wallet_balance'] = walletBalance;
    data['subtotal'] = subtotal;
    data['discount'] = discount;
    data['delivery_fee'] = deliveryFee;
    data['service_fee_percentage'] = serviceFeePercentage;
    data['service_fee'] = serviceFee;
    data['wallet_credits'] = walletCredits;
    data['tax_percentage'] = taxPercentage;
    data['taxes_and_fees'] = taxesAndFees;
    data['total'] = total;
    return data;
  }
}

class CateringInfo {
  int? id;
  int? chefId;
  int? peopleCount;
  String? date;
  int? timeSlotId;
  LocationData? location;
  String? address;
  String? state;
  String? city;
  String? zipCode;
  int? dropOffOptionId;
  String? dropOffInstructions;
  int? deliveryTimeId;
  String? status;
  TimeSlot? timeSlot;

  CateringInfo({
    this.id,
    this.chefId,
    this.peopleCount,
    this.date,
    this.timeSlotId,
    this.location,
    this.address,
    this.state,
    this.city,
    this.zipCode,
    this.dropOffOptionId,
    this.dropOffInstructions,
    this.deliveryTimeId,
    this.status,
    this.timeSlot,
  });

  CateringInfo.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    chefId = json['chef_id'];
    peopleCount = json['people_count'];
    date = json['date'];
    timeSlotId = json['time_slot_id'];
    location = json['location'] != null
        ? LocationData.fromJson(json['location'])
        : null;
    address = json['address'];
    state = json['state'];
    city = json['city'];
    zipCode = json['zip_code'];
    dropOffOptionId = json['drop_off_option_id'];
    dropOffInstructions = json['drop_off_instructions'];
    deliveryTimeId = json['delivery_time_id'];
    status = json['status'];
    timeSlot =
        json['timeSlot'] != null ? TimeSlot.fromJson(json['timeSlot']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['chef_id'] = chefId;
    data['people_count'] = peopleCount;
    data['date'] = date;
    data['time_slot_id'] = timeSlotId;
    if (location != null) {
      data['location'] = location!.toJson();
    }
    data['address'] = address;
    data['state'] = state;
    data['city'] = city;
    data['zip_code'] = zipCode;
    data['drop_off_option_id'] = dropOffOptionId;
    data['drop_off_instructions'] = dropOffInstructions;
    data['delivery_time_id'] = deliveryTimeId;
    data['status'] = status;
    if (timeSlot != null) {
      data['timeSlot'] = timeSlot!.toJson();
    }
    return data;
  }
}

class LocationData {
  CrsData? crs;
  String? type;
  List<double>? coordinates;

  LocationData({this.crs, this.type, this.coordinates});

  LocationData.fromJson(Map<String, dynamic> json) {
    crs = json['crs'] != null ? CrsData.fromJson(json['crs']) : null;
    type = json['type'];
    coordinates = json['coordinates']?.cast<double>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (crs != null) {
      data['crs'] = crs!.toJson();
    }
    data['type'] = type;
    data['coordinates'] = coordinates;
    return data;
  }
}

class CrsData {
  String? type;
  CrsProperties? properties;

  CrsData({this.type, this.properties});

  CrsData.fromJson(Map<String, dynamic> json) {
    type = json['type'];
    properties = json['properties'] != null
        ? CrsProperties.fromJson(json['properties'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['type'] = type;
    if (properties != null) {
      data['properties'] = properties!.toJson();
    }
    return data;
  }
}

class CrsProperties {
  String? name;

  CrsProperties({this.name});

  CrsProperties.fromJson(Map<String, dynamic> json) {
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['name'] = name;
    return data;
  }
}

class TimeSlot {
  int? id;
  String? startTime;
  String? endTime;
  bool? status;
  String? createdAt;
  String? updatedAt;

  TimeSlot({
    this.id,
    this.startTime,
    this.endTime,
    this.status,
    this.createdAt,
    this.updatedAt,
  });

  TimeSlot.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    startTime = json['start_time'];
    endTime = json['end_time'];
    status = json['status'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['start_time'] = startTime;
    data['end_time'] = endTime;
    data['status'] = status;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    return data;
  }
}

class CateringItem {
  int? id;
  int? quantity;
  String? dishName;
  String? dishPhoto;
  int? unitPrice;
  int? totalPrice;

  CateringItem({
    this.id,
    this.quantity,
    this.dishName,
    this.dishPhoto,
    this.unitPrice,
    this.totalPrice,
  });

  CateringItem.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    quantity = json['quantity'];
    dishName = json['dish_name'];
    dishPhoto = json['dish_photo'];
    unitPrice = json['unit_price'];
    totalPrice = json['total_price'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['quantity'] = quantity;
    data['dish_name'] = dishName;
    data['dish_photo'] = dishPhoto;
    data['unit_price'] = unitPrice;
    data['total_price'] = totalPrice;
    return data;
  }
}
