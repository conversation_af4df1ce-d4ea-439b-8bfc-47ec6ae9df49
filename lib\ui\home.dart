// ignore_for_file: deprecated_member_use, unnecessary_const

import 'dart:developer' as developer show log;
import 'package:db_eats/bloc/home_bloc.dart';
import 'package:db_eats/common/initializer.dart';
import 'package:db_eats/data/models/guesthome/guesthomemodel.dart';
import 'package:db_eats/server/serverhelper.dart';
import 'package:db_eats/ui/auth/login.dart';
import 'package:db_eats/ui/auth/signup.dart';
import 'package:db_eats/widgets/cheflocationmodal.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:geolocator/geolocator.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:shimmer/shimmer.dart';

class Home extends StatefulWidget {
  const Home({super.key});

  @override
  State<Home> createState() => _HomeState();
}

class _HomeState extends State<Home> {
  String? _currentAddress;

  @override
  void initState() {
    super.initState();
    _loadInitialAddress();
  }

  Future<void> _handleChefLocationModal() async {
    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (_) => const ChefLocationModal(type: 'guest'),
    );

    if (result == true) {
      await _loadInitialAddress();
      setState(() {});
    }
  }

  Future<void> _loadInitialAddress() async {
    try {
      _currentAddress = await Initializer.getAddress();
      double? latitude = double.tryParse(Initializer.latitude ?? '');
      double? longitude = double.tryParse(Initializer.longitude ?? '');

      if (_currentAddress == null ||
          _currentAddress!.isEmpty ||
          latitude == null ||
          longitude == null) {
        // No valid address or coordinates, show modal
        await _handleChefLocationModal();
      } else {
        // Dispatch GetGuestHomeDataEvent with coordinates
        context.read<HomeBloc>().add(GetGuestHomeDataEvent(
              latitude: latitude,
              longitude: longitude,
            ));
      }
    } catch (e) {
      developer.log('Error loading address or coordinates: $e');
      _currentAddress = 'Error loading address';
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to load address: $e')),
      );
    }
    setState(() {});
  }

  Future<Position?> _getCurrentLocation() async {
    bool serviceEnabled;
    LocationPermission permission;

    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Location services are disabled')),
      );
      return null;
    }

    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Location permissions are denied')),
        );
        return null;
      }
    }

    if (permission == LocationPermission.deniedForever) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Location permissions are permanently denied'),
        ),
      );
      return null;
    }

    try {
      return await Geolocator.getCurrentPosition();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error getting location: $e')),
      );
      return null;
    }
  }

  void showLoginWarningDialog(
      BuildContext context, double screenWidth, double screenHeight) {
    showDialog(
      context: context,
      barrierColor: Colors.black.withOpacity(0.5),
      builder: (context) => AnimatedOpacity(
        opacity: 1.0,
        duration: const Duration(milliseconds: 200),
        child: AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(screenWidth * 0.05),
          ),
          backgroundColor: Colors.white,
          elevation: 8,
          contentPadding: EdgeInsets.all(screenWidth * 0.06),
          title: Text(
            'Login Required !',
            style: TextStyle(
              fontSize: screenWidth * 0.055,
              fontWeight: FontWeight.w700,
              fontFamily: 'Inter',
              color: const Color(0xFF1F2122),
            ),
            textAlign: TextAlign.center,
          ),
          content: Text(
            'Please login to access this feature.',
            style: TextStyle(
              fontSize: screenWidth * 0.045,
              fontFamily: 'Inter-medium',
              color: const Color(0xFF66696D),
              height: 1.4,
            ),
            textAlign: TextAlign.center,
          ),
          actionsAlignment: MainAxisAlignment.center,
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              style: TextButton.styleFrom(
                padding: EdgeInsets.symmetric(
                  horizontal: screenWidth * 0.05,
                  vertical: screenHeight * 0.015,
                ),
              ),
              child: Text(
                'Cancel',
                style: TextStyle(
                  fontSize: screenWidth * 0.045,
                  fontFamily: 'Inter-Semibold',
                  color: const Color(0xFF66696D),
                ),
              ),
            ),
            SizedBox(width: screenWidth * 0.02),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => const Login()),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF1F2122),
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(screenWidth * 0.09),
                ),
                padding: EdgeInsets.symmetric(
                  horizontal: screenWidth * 0.06,
                  vertical: screenHeight * 0.010,
                ),
                elevation: 1,
              ),
              child: Text(
                'Login',
                style: TextStyle(
                  fontSize: screenWidth * 0.045,
                  fontFamily: 'Inter-Semibold',
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    return Scaffold(
      backgroundColor: const Color.fromRGBO(246, 243, 236, 1),
      body: SafeArea(
        child: Column(
          children: [
            _buildLocationHeader(context, screenWidth, screenHeight),
            SizedBox(height: screenHeight * 0.01),
            Expanded(
              child: BlocBuilder<HomeBloc, HomeState>(
                builder: (context, state) {
                  if (state is GuestHomeLoading) {
                    return Center(
                      child: LoadingAnimationWidget.staggeredDotsWave(
                        color: const Color(0xFF1F2122),
                        size: screenWidth * 0.125,
                      ),
                    );
                  }

                  if (state is GuestHomeSuccess) {
                    final data = state.data.data!;
                    return SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildFilterButton(
                              context, screenWidth, screenHeight),
                          SizedBox(height: screenHeight * 0.02),
                          _buildSectionHeader(context, screenWidth,
                              screenHeight, 'Explore Deals'),
                          SizedBox(height: screenHeight * 0.02),
                          _buildPromoCards(context, screenWidth, screenHeight,
                              data.deals!.toList()),
                          SizedBox(height: screenHeight * 0.02),
                          _buildSectionHeader(context, screenWidth,
                              screenHeight, 'Explore Dishes'),
                          SizedBox(height: screenHeight * 0.0175),
                          _buildDishGrid(context, screenWidth, screenHeight,
                              data.dishes ?? []),
                          SizedBox(height: screenHeight * 0.02),
                          _buildMealPlanCard(
                              context, screenWidth, screenHeight),
                          _buildMonthlySaverCard(
                              context, screenWidth, screenHeight),
                          SizedBox(height: screenHeight * 0.015),
                          _buildSectionHeader(context, screenWidth,
                              screenHeight, 'Top-Rated Chefs'),
                          SizedBox(height: screenHeight * 0.02),
                          _buildTopRatedChefs(context, screenWidth,
                              screenHeight, data.topRatedChefs ?? []),
                          SizedBox(height: screenHeight * 0.025),
                          _buildSectionHeader(context, screenWidth,
                              screenHeight, 'Most Popular Near You'),
                          SizedBox(height: screenHeight * 0.02),
                          _buildTopRatedChefs(context, screenWidth,
                              screenHeight, data.popularChefsNear ?? []),
                          SizedBox(height: screenHeight * 0.03),
                        ],
                      ),
                    );
                  }

                  return SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildFilterButton(context, screenWidth, screenHeight),
                        SizedBox(height: screenHeight * 0.0175),
                        _buildShimmerSectionHeader(
                            context, screenWidth, screenHeight),
                        SizedBox(height: screenHeight * 0.0175),
                        _buildShimmerPromoCards(
                            context, screenWidth, screenHeight),
                        SizedBox(height: screenHeight * 0.02),
                        _buildShimmerSectionHeader(
                            context, screenWidth, screenHeight),
                        SizedBox(height: screenHeight * 0.0175),
                        _buildShimmerDishGrid(
                            context, screenWidth, screenHeight),
                        SizedBox(height: screenHeight * 0.02),
                        _buildShimmerSectionHeader(
                            context, screenWidth, screenHeight),
                        SizedBox(height: screenHeight * 0.02),
                        _buildShimmerChefList(
                            context, screenWidth, screenHeight),
                        SizedBox(height: screenHeight * 0.025),
                        _buildShimmerSectionHeader(
                            context, screenWidth, screenHeight),
                        SizedBox(height: screenHeight * 0.02),
                        _buildShimmerChefList(
                            context, screenWidth, screenHeight),
                        SizedBox(height: screenHeight * 0.03),
                      ],
                    ),
                  );
                },
              ),
            ),
            _buildAuthButtons(context, screenWidth, screenHeight),
          ],
        ),
      ),
    );
  }

  Widget _buildLocationHeader(
      BuildContext context, double screenWidth, double screenHeight) {
    return Column(
      children: [
        Padding(
          padding: EdgeInsets.fromLTRB(screenWidth * 0.04, screenHeight * 0.015,
              screenWidth * 0.04, screenHeight * 0.01),
          child: Row(
            children: [
              GestureDetector(
                onTap: () =>
                    showLoginWarningDialog(context, screenWidth, screenHeight),
                child: Icon(
                  Icons.menu,
                  size: screenWidth * 0.06,
                  color: const Color.fromRGBO(31, 33, 34, 1),
                ),
              ),
              SizedBox(width: screenWidth * 0.03),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    GestureDetector(
                      onTap: _handleChefLocationModal,
                      child: Row(
                        children: [
                          Image.asset(
                            'assets/icons/location3.png',
                            width: screenWidth * 0.035,
                            height: screenWidth * 0.035,
                            fit: BoxFit.contain,
                          ),
                          SizedBox(width: screenWidth * 0.01),
                          Expanded(
                            child: Text(
                              _currentAddress ?? 'Select an address',
                              style: TextStyle(
                                fontSize: twelve,
                                fontWeight: FontWeight.w500,
                                fontFamily: 'Inter',
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          // Text(
                          //   'Change',
                          //   style: TextStyle(
                          //     fontSize: screenWidth * 0.03,
                          //     color: Colors.blue,
                          //     fontWeight: FontWeight.w600,
                          //   ),
                          // ),
                        ],
                      ),
                    ),
                    SizedBox(height: screenHeight * 0.005),
                    Row(
                      children: [
                        Icon(Icons.access_time,
                            size: screenWidth * 0.03,
                            color: const Color.fromRGBO(31, 33, 34, 1)),
                        SizedBox(width: screenWidth * 0.015),
                        Text(
                          'ASAP',
                          style: TextStyle(
                            fontSize: twelve,
                            fontWeight: FontWeight.w400,
                            fontFamily: 'Inter',
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              Icon(Icons.search,
                  size: screenWidth * 0.06,
                  color: const Color.fromRGBO(31, 33, 34, 1)),
            ],
          ),
        ),
        const Divider(height: 2, color: Color.fromARGB(255, 219, 212, 212)),
      ],
    );
  }

  Widget _buildFilterButton(
      BuildContext context, double screenWidth, double screenHeight) {
    return Padding(
      padding: EdgeInsets.symmetric(
          horizontal: screenWidth * 0.04, vertical: screenHeight * 0.015),
      child: GestureDetector(
        onTap: () => showLoginWarningDialog(context, screenWidth, screenHeight),
        child: Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(vertical: screenHeight * 0.0175),
          decoration: BoxDecoration(
            border: Border.all(
              color: const Color.fromRGBO(31, 33, 34, 1),
              width: 1.5,
            ),
            borderRadius: BorderRadius.circular(screenWidth * 0.075),
            color: const Color.fromRGBO(246, 243, 236, 1),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.tune,
                  size: screenWidth * 0.045, color: Colors.grey[800]),
              SizedBox(width: screenWidth * 0.02),
              Text(
                'View Filters',
                style: TextStyle(
                  fontSize: twelve,
                  fontWeight: FontWeight.w600,
                  fontFamily: 'Inter',
                  color: const Color.fromRGBO(31, 33, 34, 1),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, double screenWidth,
      double screenHeight, String title) {
    return Padding(
      padding: EdgeInsets.fromLTRB(screenWidth * 0.04, screenWidth * 0.03,
          screenWidth * 0.04, screenWidth * 0.01),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: screenWidth * 0.04584,
            fontWeight: FontWeight.w600,
              fontFamily: 'suisse-intl',
              height: 1,
            ),
          ),
          GestureDetector(
            onTap: () =>
                showLoginWarningDialog(context, screenWidth, screenHeight),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'See All',
                  style: TextStyle(
                    fontSize: screenWidth * 0.03,
               fontWeight: FontWeight.w600,
                    height: 0.9,
                  ),
                ),
                SizedBox(height: screenWidth * 0.0025),
                Container(
                  height: 1.2,
                  width: screenWidth * 0.09,
                  color: Colors.black54,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPromoCards(BuildContext context, double screenWidth,
      double screenHeight, List<Deal> deals) {
    // Define responsive font sizes and padding based on screen width
    final double baseFontSize =
        screenWidth * 0.035; // Base font size for scaling
    final double cardWidth = screenWidth * 0.45; // Card width as percentage
    final double cardHeight = screenHeight * 0.25; // Card height as percentage
    final double imageHeight = screenHeight * 0.12; // Image height
    final double padding = screenWidth * 0.025; // Responsive padding
    final double separatorWidth = screenWidth * 0.03; // Space between cards
    final double iconSize = screenWidth * 0.1; // Icon size for empty state
    final double avatarRadius = screenWidth * 0.025; // Avatar size
    final double borderRadius = screenWidth * 0.03; // Card corner radius

    // Adjust for larger screens (e.g., tablets)
    final bool isLargeScreen = screenWidth > 600;
    final double adjustedCardWidth =
        isLargeScreen ? screenWidth * 0.3 : cardWidth;
    final double adjustedFontSize =
        isLargeScreen ? baseFontSize * 1.2 : baseFontSize;

    if (deals.isEmpty) {
      return Padding(
        padding: EdgeInsets.symmetric(
          horizontal: screenWidth * 0.04,
          vertical: screenHeight * 0.02,
        ),
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.local_offer_outlined,
                size: screenWidth * 0.1,
                color: const Color(0xFF66696D),
              ),
              SizedBox(height: screenHeight * 0.01),
              Text(
                'No deals available',
                style: TextStyle(
                  fontSize: screenWidth * 0.035,
                  fontWeight: FontWeight.w400,
                  fontFamily: 'Inter',
                  color: const Color(0xFF66696D),
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return SizedBox(
      height: screenHeight * 0.23,
      child: ListView.separated(
        padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.04),
        scrollDirection: Axis.horizontal,
        itemCount: deals.length,
        separatorBuilder: (_, __) => SizedBox(width: screenWidth * 0.03),
        itemBuilder: (_, index) {
          final deal = deals[index];
          return GestureDetector(
            onTap: () =>
                showLoginWarningDialog(context, screenWidth, screenHeight),
            child: Container(
              width: adjustedCardWidth,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(screenWidth * 0.03),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: screenWidth * 0.02,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(screenWidth * 0.03),
                      topRight: Radius.circular(screenWidth * 0.03),
                    ),
                    child: Image.network(
                      deal.chef?.profile?.coverPhoto != null
                          ? ServerHelper.imageUrl +
                              deal.chef!.profile!.coverPhoto!
                          : 'https://via.placeholder.com/180x100.png?text=Deal',
                      height: screenHeight * 0.12,
                      width: double.infinity,
                      fit: BoxFit.cover,
                      loadingBuilder: (context, child, loadingProgress) {
                        if (loadingProgress == null) return child;
                        return Container(
                          height: screenHeight * 0.12,
                          width: double.infinity,
                          color: Colors.grey[300],
                          child: Center(
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                  Colors.grey[600]!),
                            ),
                          ),
                        );
                      },
                      errorBuilder: (context, error, stackTrace) => Container(
                        height: screenHeight * 0.12,
                        width: double.infinity,
                        color: Colors.grey[300],
                        child: Icon(
                          Icons.broken_image,
                          size: screenWidth * 0.1,
                          color: Colors.grey[600],
                        ),
                      ),
                    ),
                  ),
                  Expanded(
                    child: Padding(
                      padding: EdgeInsets.all(padding),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            deal.title ?? 'Deal Title',
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              fontSize: screenWidth * 0.03565,
                              fontFamily: 'Inter',
                              height: 1.2,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          Row(
                            children: [
                              CircleAvatar(
                                radius: screenWidth * 0.025,
                                backgroundImage: NetworkImage(
                                  deal.chef?.profile?.profilePhoto != null
                                      ? ServerHelper.imageUrl +
                                          deal.chef!.profile!.profilePhoto!
                                      : 'https://via.placeholder.com/40x40.png?text=Chef',
                                ),
                                backgroundColor: Colors.grey[300],
                                onBackgroundImageError: (_, __) => Icon(
                                  Icons.person,
                                  size: avatarRadius,
                                  color: Colors.grey[600],
                                ),
                              ),
                              SizedBox(width: screenWidth * 0.015),
                              Expanded(
                                child: Text(
                                  '${_capitalizeFirst(deal.chef?.firstName ?? '')} ${_capitalizeFirst(deal.chef?.lastName ?? '')}',
                                  style: TextStyle(
                                    fontSize: screenWidth * 0.03,
//                                    fontWeight: FontWeight.bold,
                                    fontWeight: FontWeight.w500,
                                    color: const Color(0xFF1F2122),
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [

                                 IntrinsicWidth(
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Text(
                                    'View Deal',
                                    style: TextStyle(
                                      fontFamily: 'Inter',
                                      fontSize: screenWidth * 0.025,
                                      fontWeight: FontWeight.w500,
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                  const SizedBox(height: 0.8),
                                  Container(
                                    height: 1,
                                    width: double
                                        .infinity, // Takes the width of the IntrinsicWidth
                                    color: const Color(0xFF1F2122),
                                  ),
                                ],
                              ),
                            ),
                              // Text(
                              //   'View Deal',
                              //   style: TextStyle(
                              //     fontSize: screenWidth * 0.025,
                              //     fontWeight: FontWeight.w500,
                              //     fontFamily: 'Inter',
                              //     color: const Color(0xFF1F2122),
                              //   //  decoration: TextDecoration.underline
                              //   ),
                              //   strutStyle: StrutStyle(leading: 0.5,
                              //   ),
                              // ),
                              // const SizedBox(height: 1), // s
                              // Container(
                              //   height: 1.2,
                              //   width: screenWidth *
                              //       0.1223, // or use based on text length
                              //   //1249
                              //   color: const Color(0xFF1F2122),
                              // ),
                            ],

                            //      Text(
                            //   'View Deal',
                            //   style: TextStyle(
                            //     fontFamily: 'Inter',
                            //     fontSize: screenWidth * 0.025,
                            //     fontWeight: FontWeight.w500,
                            //     color: const Color(0xFF1F2122),
                            //     // no decoration here
                            // //   decoration: TextDecoration.underline,
                            //   ),strutStyle: StrutStyle(
                            // leading: 0.05
                            //   ),
                            // ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  String _capitalizeFirst(String text) {
    if (text.isEmpty) return '';
    return text[0].toUpperCase() + (text.length > 1 ? text.substring(1) : '');
  }

  late double ten;
  late double twelve;
  late double forteen;
  late double sixteen;
  late double eighteen;
  late double twenty;
  late double twentyFour;
  late double screenWidth;
  late double screenHeight;
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    screenWidth = MediaQuery.of(context).size.width;
    screenHeight = MediaQuery.of(context).size.height;

    ten = screenWidth * 0.02545;
    twelve = screenWidth * 0.03054;
    forteen = screenWidth * 0.035;
    sixteen = screenWidth * 0.04073;
    eighteen = screenWidth * 0.04582;
    twenty = screenWidth * 0.05091;
    twentyFour = screenWidth * 0.06109;
  }

  Widget _buildDishGrid(BuildContext context, double screenWidth,
      double screenHeight, List<Dishes> dishes) {
    if (dishes.isEmpty) {
      return Padding(
        padding: EdgeInsets.symmetric(
            horizontal: screenWidth * 0.04, vertical: screenHeight * 0.01),
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Icon(Icons.restaurant_menu,
              //     size: screenWidth * 0.1, color: const Color(0xFF66696D)),
              Image.asset(
                'assets/icons/no_data.png',
                width: screenWidth * 0.5,
                height: screenWidth * 0.5,
              ),
              SizedBox(height: screenHeight * 0.02),
              Text(
                'No dishes available',
                style: TextStyle(
                  fontSize: screenWidth * 0.045,
                  fontWeight: FontWeight.w400,
                  fontFamily: 'Inter',
                  color: const Color(0xFF66696D),
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }
    return SizedBox(
      height: ten * 26 + sixteen,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.04),
        itemCount: dishes.length,
        separatorBuilder: (_, __) => SizedBox(width: screenWidth * 0.01),
        itemBuilder: (_, index) {
          final dish = dishes[index];
          final price = dish.servingSizePrices?.first.price ?? '0.00';
          final servings =
              '${dish.servingSizePrices?.first.servingSize?.serves ?? 1} Serving';

          return Container(
            width: ten * 20 + eighteen,
            margin: EdgeInsets.only(right: screenWidth * 0.04),
            child: _buildDishCard(
              context,
              screenWidth,
              screenHeight,
              image: ServerHelper.imageUrl + (dish.photo ?? ''),
              title: dish.name ?? 'Untitled',
              servings: servings,
              rating: '82% (49)',
              price: '\$$price',
            ),
          );
        },
      ),
    );
  }

  Widget _buildDishCard(
    BuildContext context,
    double screenWidth,
    double screenHeight, {
    required String image,
    required String title,
    required String servings,
    required String rating,
    required String price,
  }) {
    return GestureDetector(
      onTap: () => showLoginWarningDialog(context, screenWidth, screenHeight),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(screenWidth * 0.04),
          border: Border.all(color: const Color(0xFFECECEC)),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.vertical(
                  top: Radius.circular(screenWidth * 0.04)),
              child: Image.network(
                image,
                height: ten * 15 + twelve,
                width: double.infinity,
                fit: BoxFit.cover,
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) {
                    return AnimatedOpacity(
                      opacity: 1.0,
                      duration: const Duration(milliseconds: 500),
                      child: child,
                    );
                  }
                  return Container(
                    width: double.infinity,
                    height: ten * 15 + twelve,
                    color: Colors.grey[200],
                    child: Center(
                      child: LoadingAnimationWidget.staggeredDotsWave(
                        color: const Color(0xFF1F2122),
                        size: screenWidth * 0.1,
                      ),
                    ),
                  );
                },
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    width: double.infinity,
                    height: ten * 15 + twelve,
                    color: Colors.grey[300],
                    child: Icon(Icons.restaurant,
                        color: Colors.grey[400], size: screenWidth * 0.1),
                  );
                },
              ),
            ),
            Padding(
              padding: EdgeInsets.only(
                  top: screenWidth * 0.025,
                  bottom: screenWidth * 0.015,
                  right: screenWidth * 0.04,
                  left: screenWidth * 0.04),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: forteen,
                      fontWeight: FontWeight.w500,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: screenHeight * 0.0125),
                  Row(
                    children: [
                      _pillText(context, screenWidth, screenHeight, servings),
                      SizedBox(width: screenWidth * 0.02),
                      _ratingPill(context, screenWidth, screenHeight, rating),
                    ],
                  ),
                  SizedBox(height: screenHeight * 0.02),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        price,
                        style: TextStyle(
                          fontFamily: 'Roboto',
                          fontSize: twelve,
                          //            fontWeight: FontWeight.bold,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      GestureDetector(
                        onTap: () => showLoginWarningDialog(
                            context, screenWidth, screenHeight),
                        child: Container(
                          alignment: Alignment.bottomRight,
                          child: Image.asset(
                            'assets/icons/add.png',
                            width: forteen + ten,
                            height: forteen + ten,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _pillText(BuildContext context, double screenWidth,
      double screenHeight, String text) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: screenWidth * 0.015,
        vertical: screenWidth * 0.005,
      ),
      decoration: BoxDecoration(
        color: const Color(0xFFE1E3E6),
        borderRadius: BorderRadius.circular(screenWidth * 0.03),
      ),
      child: Text(
        text,
        style: TextStyle(
            fontSize: ten,
            fontWeight: FontWeight.w500,
            fontFamily: 'Inter',
            color: const Color(0xFF1F2122),
            height: 1.0,
            letterSpacing: 0.2),
      ),
    );
  }

  Widget _ratingPill(BuildContext context, double screenWidth,
      double screenHeight, String rating) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: screenWidth * 0.015,
        vertical: screenWidth * 0.005,
      ),
      decoration: BoxDecoration(
        color: const Color.fromRGBO(225, 227, 230, 1),
        borderRadius: BorderRadius.circular(screenWidth * 0.03),
      ),
      child: Row(
        children: [
          Image.asset(
            'assets/icons/thump.png',
            width: screenWidth * 0.03,
            height: screenWidth * 0.0275,
            color: Colors.black54,
          ),
          SizedBox(width: screenWidth * 0.01),
          Text(
            rating,
            style: TextStyle(
              fontSize: ten,
              fontWeight: FontWeight.w500,
              fontFamily: 'Inter',
              height: 1.0,
              letterSpacing: 0.24,
              color: const Color(0xFF1F2122),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMealPlanCard(
      BuildContext context, double screenWidth, double screenHeight) {
    return Padding(
      padding: EdgeInsets.all(screenWidth * 0.04),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.black,
          borderRadius: BorderRadius.circular(screenWidth * 0.04),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(screenWidth * 0.04),
                topRight: Radius.circular(screenWidth * 0.04),
              ),
              child: Image.asset(
                'assets/images/weekly_plan.png',
                width: double.infinity,
                height: screenHeight * 0.2,
                fit: BoxFit.cover,
              ),
            ),
            Padding(
              padding: EdgeInsets.all(screenWidth * 0.03),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        decoration: const BoxDecoration(
                          color: Color(0xFFFFBE16),
                          shape: BoxShape.circle,
                        ),
                        width: screenWidth * 0.067,
                        height: screenWidth * 0.067,
                        child: Padding(
                          padding: EdgeInsets.all(screenWidth * 0.02),
                          child: Image.asset(
                            'assets/icons/date_range.png',
                            width: screenWidth * 0.04,
                            height: screenWidth * 0.04,
                            fit: BoxFit.contain,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      SizedBox(width: screenWidth * 0.02),
                      Text(
                        'Weekly Meal Plan',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: screenWidth * 0.04584,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: screenHeight * 0.008),
                  Text(
                    'Subscribe to curated dishes delivered weekly. Dishes starting at \$9, skip or cancel anytime.',
                    style: TextStyle(
                      color: const Color(0xFFAAADB1),
                      fontSize: screenWidth * 0.03,
                      height: 1.4,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  SizedBox(height: screenHeight * 0.01),
                  Padding(
                    padding: EdgeInsets.only(left: screenWidth * 0.02),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Icon(Icons.circle,
                                size: screenWidth * 0.015,
                                color: const Color(0xFFAAADB1)),
                            SizedBox(width: screenWidth * 0.02),
                            Text('Discounted Pricing',
                                style: TextStyle(
                                    color: const Color(0xFFAAADB1),
                                    fontWeight: FontWeight.w400,
                                    fontSize: screenWidth * 0.03)),
                          ],
                        ),
                        SizedBox(height: screenHeight * 0.003),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Icon(Icons.circle,
                                size: screenWidth * 0.015,
                                color: const Color(0xFFAAADB1)),
                            SizedBox(width: screenWidth * 0.02),
                            Text('Free Delivery',
                                style: TextStyle(
                                    color: const Color(0xFFAAADB1),
                                    fontWeight: FontWeight.w400,
                                    fontSize: screenWidth * 0.03)),
                          ],
                        ),
                        SizedBox(height: screenHeight * 0.003),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Icon(Icons.circle,
                                size: screenWidth * 0.015,
                                color: const Color(0xFFAAADB1)),
                            SizedBox(width: screenWidth * 0.02),
                            Text('Top-Rated Chefs',
                                style: TextStyle(
                                    color: const Color(0xFFAAADB1),
                                    fontWeight: FontWeight.w400,
                                    fontSize: screenWidth * 0.03)),
                          ],
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: screenHeight * 0.015),
                  GestureDetector(
                    onTap: () => showLoginWarningDialog(
                        context, screenWidth, screenHeight),
                    child: Container(
                      width: double.infinity,
                      padding:
                          EdgeInsets.symmetric(vertical: screenWidth * 0.035),
                      decoration: BoxDecoration(
                        border: Border.all(color: const Color(0xFFAAADB1)),
                        borderRadius:
                            BorderRadius.circular(screenWidth * 0.075),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            'Start A Meal Plan',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: screenWidth * 0.03,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          SizedBox(width: screenWidth * 0.02),
                          Icon(Icons.arrow_forward,
                              color: Colors.white, size: screenWidth * 0.04),
                        ],
                      ),
                    ),
                  ),
                  //    SizedBox(height: screenHeight * 0.015),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMonthlySaverCard(
      BuildContext context, double screenWidth, double screenHeight) {
    return Padding(
      padding: EdgeInsets.all(screenWidth * 0.04),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.black,
          borderRadius: BorderRadius.circular(screenWidth * 0.04),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(screenWidth * 0.04),
                topRight: Radius.circular(screenWidth * 0.04),
              ),
              child: Image.asset(
                'assets/images/monthly_saver.png',
                width: double.infinity,
                height: screenHeight * 0.19,
                fit: BoxFit.cover,
              ),
            ),
            Padding(
              padding: EdgeInsets.all(screenWidth * 0.03),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        decoration: const BoxDecoration(
                          color: Color(0xFFFFBE16),
                          shape: BoxShape.circle,
                        ),
                        width: screenWidth * 0.067,
                        height: screenWidth * 0.067,
                        child: Padding(
                          padding: EdgeInsets.all(screenWidth * 0.02),
                          child: Image.asset(
                            'assets/icons/percent.png',
                            width: screenWidth * 0.04,
                            height: screenWidth * 0.04,
                            fit: BoxFit.contain,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      SizedBox(width: screenWidth * 0.02),
                      Text(
                        "Monthly Saver's Pass",
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: screenWidth * 0.04584,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: screenHeight * 0.01),
                  Text(
                    'The all-in-one plan you need to unlock exclusive benefits across DB.',
                    style: TextStyle(
                      color: const Color(0xFFAAADB1),
                      fontSize: screenWidth * 0.03,
                      fontWeight: FontWeight.w400,
                      height: 1.4,
                    ),
                  ),
                  SizedBox(height: screenHeight * 0.01),
                  Padding(
                    padding: EdgeInsets.only(left: screenWidth * 0.02),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Icon(Icons.circle,
                                size: screenWidth * 0.015,
                                color: const Color(0xFFAAADB1)),
                            SizedBox(width: screenWidth * 0.02),
                            Text('Unlimited Free Delivery (Capped at \$2.0)',
                                style: TextStyle(
                                    color: const Color(0xFFAAADB1),
                                    fontWeight: FontWeight.w400,
                                    fontSize: screenWidth * 0.03)),
                          ],
                        ),
                        SizedBox(height: screenHeight * 0.003),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Icon(Icons.circle,
                                size: screenWidth * 0.015,
                                color: const Color(0xFFAAADB1)),
                            SizedBox(width: screenWidth * 0.02),
                            Text('Up to 30% Off Restaurants',
                                style: TextStyle(
                                  color: const Color(0xFFAAADB1),
                                  fontSize: screenWidth * 0.03,
                                  fontWeight: FontWeight.w400,
                                )),
                          ],
                        ),
                        SizedBox(height: screenHeight * 0.003),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Icon(Icons.circle,
                                size: screenWidth * 0.015,
                                color: const Color(0xFFAAADB1)),
                            SizedBox(width: screenWidth * 0.02),
                            Text('Surprise Perks',
                                style: TextStyle(
                                  color: const Color(0xFFAAADB1),
                                  fontSize: screenWidth * 0.03,
                                  fontWeight: FontWeight.w400,
                                )),
                          ],
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: screenHeight * 0.016),
                  GestureDetector(
                    onTap: () => showLoginWarningDialog(
                        context, screenWidth, screenHeight),
                    child: Container(
                      width: double.infinity,
                      padding:
                          EdgeInsets.symmetric(vertical: screenWidth * 0.035),
                      decoration: BoxDecoration(
                        border: Border.all(color: const Color(0xFFAAADB1)),
                        borderRadius:
                            BorderRadius.circular(screenWidth * 0.075),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            'Start A Meal Plan',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: screenWidth * 0.03,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          SizedBox(width: screenWidth * 0.02),
                          Icon(Icons.arrow_forward,
                              color: Colors.white, size: screenWidth * 0.04),
                        ],
                      ),
                    ),
                  ),
                  SizedBox(
                    height: 2,
                  )
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopRatedChefs(BuildContext context, double screenWidth,
      double screenHeight, List<TopRatedChefs> chefs) {
    if (chefs.isEmpty) {
      return Padding(
        padding: EdgeInsets.symmetric(
            horizontal: screenWidth * 0.04, vertical: screenHeight * 0.02),
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.person,
                  size: screenWidth * 0.1, color: const Color(0xFF66696D)),
              SizedBox(height: screenHeight * 0.01),
              Text(
                'No chefs available',
                style: TextStyle(
                  fontSize: screenWidth * 0.04,
                  fontWeight: FontWeight.w400,
                  fontFamily: 'Inter',
                  color: const Color(0xFF66696D),
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }
    return SizedBox(
      height: ten * 26,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.04),
        itemCount: chefs.length,
        separatorBuilder: (_, __) => SizedBox(width: screenWidth * 0.03),
        itemBuilder: (_, index) {
          final chef = chefs[index];
          final name =
              '${chef.chef?.firstName ?? ''} ${chef.chef?.lastName ?? ''}';
          final availability = chef.chef?.operationDays
                  ?.map((day) => day.day?.name?.substring(0, 1) ?? '')
                  .join(', ') ??
              '';
          final cuisines = chef.searchTags?.join(', ') ?? 'Various Cuisines';
          final distance = '${chef.distance?.toStringAsFixed(1) ?? 0.0} KM';

          return _buildChefCard(
            context,
            screenWidth,
            screenHeight,
            image: ServerHelper.imageUrl + (chef.profilePhoto ?? ''),
            coverPhoto: ServerHelper.imageUrl + (chef.coverPhoto ?? ''),
            name: name,
            cuisines: cuisines,
            rating: '82% (49)',
            distance: distance,
            availability: availability,
          );
        },
      ),
    );
  }

  Widget _buildChefCard(
    BuildContext context,
    double screenWidth,
    double screenHeight, {
    required String image,
    required String name,
    required String cuisines,
    required String rating,
    required String distance,
    required String availability,
    required String coverPhoto,
  }) {
    return GestureDetector(
      onTap: () => showLoginWarningDialog(context, screenWidth, screenHeight),
      child: Container(
        width: ten * 23,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(screenWidth * 0.03),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Stack(
              clipBehavior: Clip.none,
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(screenWidth * 0.03),
                    topRight: Radius.circular(screenWidth * 0.03),
                  ),
                  child: Image.network(
                    coverPhoto,
                    width: double.infinity,
                    height: ten * 12,
                    fit: BoxFit.cover,
                    loadingBuilder: (context, child, loadingProgress) {
                      if (loadingProgress == null) {
                        return AnimatedOpacity(
                          opacity: 1.0,
                          duration: const Duration(milliseconds: 500),
                          child: child,
                        );
                      }
                      return Container(
                        width: double.infinity,
                        height: ten * 12,
                        color: Colors.grey[300],
                        child: Center(
                          child: LoadingAnimationWidget.staggeredDotsWave(
                            color: const Color(0xFF1F2122),
                            size: screenWidth * 0.075,
                          ),
                        ),
                      );
                    },
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        width: double.infinity,
                        height: ten * 12,
                        color: Colors.grey[300],
                        child: Icon(Icons.image,
                            color: Colors.grey[400], size: screenWidth * 0.1),
                      );
                    },
                  ),
                ),
                Positioned(
                  top: screenWidth * 0.04,
                  left: screenWidth * 0.04,
                  child: Container(
                    padding: EdgeInsets.symmetric(
                        horizontal: screenWidth * 0.02,
                        vertical: screenWidth * 0.01),
                    decoration: BoxDecoration(
                      color: const Color(0xFF000000),
                      borderRadius: BorderRadius.circular(screenWidth * 0.03),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.access_time,
                            size: screenWidth * 0.03, color: Colors.white),
                        SizedBox(width: screenWidth * 0.01),
                        Text(
                          '35 mins',
                          style: TextStyle(
                            fontSize: screenWidth * 0.03,
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                Positioned(
                  left: screenWidth * 0.04,
                  bottom: -screenWidth * 0.05,
                  child: Container(
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.white, width: 3),
                    ),
                    child: CircleAvatar(
                      radius: screenWidth * 0.075,
                      backgroundImage: NetworkImage(image),
                      onBackgroundImageError: (_, __) => null,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: screenHeight * 0.03),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.04),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    name,
                    style: TextStyle(
                      fontSize: forteen,
                      fontWeight: FontWeight.w400,
                      fontFamily: 'Inter',
                    ),
                  ),
                  SizedBox(height: screenHeight * 0.005),
                  Text(
                    cuisines,
                    style: TextStyle(
                      fontSize: twelve,
                      fontWeight: FontWeight.w400,
                      fontFamily: 'Inter',
                      color: const Color.fromRGBO(65, 67, 70, 1),
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: screenHeight * 0.01),
                  Row(
                    children: [
                      Container(
                        padding: EdgeInsets.symmetric(
                            horizontal: screenWidth * 0.015,
                            vertical: screenHeight * 0.005),
                        decoration: BoxDecoration(
                          color: const Color.fromRGBO(225, 227, 230, 1),
                          borderRadius:
                              BorderRadius.circular(screenWidth * 0.03),
                        ),
                        child: Row(
                          children: [
                            Image.asset(
                              'assets/icons/thump.png',
                              width: screenWidth * 0.0275,
                              height: screenWidth * 0.025,
                              color: Color(0xff1F2122),
                            ),
                            SizedBox(width: screenWidth * 0.01),
                            Text(
                              rating,
                              style: TextStyle(
                                fontSize: ten,color: Color(0xff1F2122),
                                fontWeight: FontWeight.w500,
                                fontFamily: 'Inter',
                              ),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(width: screenWidth * 0.02),
                      Row(
                        children: [
                          Icon(Icons.location_on_outlined,
                              size: screenWidth * 0.03, color: Colors.black54),
                          SizedBox(width: screenWidth * 0.005),
                          Text(
                            distance,
                            style: TextStyle(
                              fontSize: ten,
                              fontWeight: FontWeight.w400,
                              fontFamily: 'Inter',
                              color:Color(0xff414346),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  SizedBox(height: screenHeight * 0.005),
                  Row(
                    children: [
                      Image.asset(
                        'assets/icons/calender_2.png',
                        width: screenWidth * 0.03,
                        height: screenWidth * 0.0325,
                       color:Color(0xff414346),
                      ),
                      SizedBox(width: screenWidth * 0.01),
                      Text(
                        availability,
                        style: TextStyle(
                          fontSize: twelve,
                          fontFamily: 'Inter',
                          fontWeight: FontWeight.w400,
                          color: Colors.black54,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAuthButtons(
      BuildContext context, double screenWidth, double screenHeight) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(
              horizontal: screenWidth * 0.04, vertical: screenHeight * 0.02),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(screenWidth * 0.04),
              topRight: Radius.circular(screenWidth * 0.04),
            ),
          ),
          child: Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const Signup(),
                      ),
                    );
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.black,
                    foregroundColor: Colors.white,
                    padding:
                        EdgeInsets.symmetric(vertical: screenHeight * 0.02),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(screenWidth * 0.08),
                    ),
                    elevation: 0,
                  ),
                  child: Text(
                    'Sign Up',
                    style: TextStyle(
                      fontSize: screenWidth * 0.04,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ),
              ),
              SizedBox(width: screenWidth * 0.04),
              Expanded(
                child: OutlinedButton(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const Login(),
                      ),
                    );
                  },
                  style: OutlinedButton.styleFrom(
                    foregroundColor: Colors.black,
                    side: const BorderSide(color: Colors.black, width: 1),
                    padding:
                        EdgeInsets.symmetric(vertical: screenHeight * 0.02),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(screenWidth * 0.08),
                    ),
                  ),
                  child: Text(
                    'Log In',
                    style: TextStyle(
                      fontSize: screenWidth * 0.04,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildShimmerSectionHeader(
      BuildContext context, double screenWidth, double screenHeight) {
    return Padding(
      padding: EdgeInsets.fromLTRB(screenWidth * 0.04, screenHeight * 0.015,
          screenWidth * 0.04, screenHeight * 0.005),
      child: Shimmer.fromColors(
        baseColor: Colors.grey[300]!,
        highlightColor: Colors.grey[100]!,
        child: Container(
          width: screenWidth * 0.5,
          height: screenHeight * 0.04,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(screenWidth * 0.01),
          ),
        ),
      ),
    );
  }

  Widget _buildShimmerPromoCards(
      BuildContext context, double screenWidth, double screenHeight) {
    return SizedBox(
      height: screenHeight * 0.25,
      child: ListView.separated(
        padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.04),
        scrollDirection: Axis.horizontal,
        itemCount: 2,
        separatorBuilder: (_, __) => SizedBox(width: screenWidth * 0.03),
        itemBuilder: (_, index) => Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[100]!,
          child: Container(
            width: screenWidth * 0.45,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(screenWidth * 0.03),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildShimmerDishGrid(
      BuildContext context, double screenWidth, double screenHeight) {
    return SizedBox(
      height: screenHeight * 0.4,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.04),
        itemCount: 3,
        separatorBuilder: (_, __) => SizedBox(width: screenWidth * 0.03),
        itemBuilder: (_, index) => Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[100]!,
          child: Container(
            width: screenWidth * 0.7,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(screenWidth * 0.03),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildShimmerChefList(
      BuildContext context, double screenWidth, double screenHeight) {
    return SizedBox(
      height: screenHeight * 0.325,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.04),
        itemCount: 3,
        separatorBuilder: (_, __) => SizedBox(width: screenWidth * 0.03),
        itemBuilder: (_, index) => Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[100]!,
          child: Container(
            width: screenWidth * 0.6,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(screenWidth * 0.03),
            ),
          ),
        ),
      ),
    );
  }
}
