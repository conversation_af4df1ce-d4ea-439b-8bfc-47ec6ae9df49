import 'package:db_eats/ui/cart/edit_address.dart';
import 'package:db_eats/ui/meal_plan/curated.dart';
import 'package:db_eats/ui/mainnavigationpage.dart';
import 'package:db_eats/ui/meal_plan/personailized_selectchef.dart';
import 'package:db_eats/utils/customtoggle.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:db_eats/bloc/mealplan_bloc.dart';
import 'package:intl/intl.dart';
import 'package:shimmer/shimmer.dart';
import 'package:db_eats/data/models/meal_plan/mealplanprogressmodel.dart';
import 'package:db_eats/server/serverhelper.dart';
import 'package:db_eats/data/models/meal_plan/dropoffoptionmodel.dart';
import 'package:db_eats/data/models/meal_plan/deliverytimemodel.dart';
import 'package:db_eats/data/models/guesthome/listaddressmodel.dart';
import 'package:db_eats/bloc/account_bloc.dart';

class CheckoutPage extends StatefulWidget {
  final int mealPlanId;
  final List<Map<String, dynamic>>? selectedChefsWithDetails;

  const CheckoutPage({
    super.key,
    required this.mealPlanId,
    this.selectedChefsWithDetails,
  });

  @override
  State<CheckoutPage> createState() => _CheckoutPageState();
}

class _CheckoutPageState extends State<CheckoutPage> {
  String? dropOffOption;
  String? instructions;
  String? selectedDeliveryOption;
  Data? mealPlanData;
  List<DropoffOptionItem> _dropoffOptions = [];
  List<DeliveryTimeItem> _deliveryTimeOptions = [];
  DropoffOptionItem? _selectedDropoffOption;
  DeliveryTimeItem? _selectedDeliveryTime;
  String? _dropOffInstructions;
  bool _showDropoffError = false;
  bool _showDeliveryTimeError = false;
  bool _isPlacingOrder = false;
  late TextEditingController _instructionsController;
  AddressData? currentAddressData;
  String? currentAddress;
  DateTime selectedDate = DateTime.now();
  String selectedTime = '';

  String _getCurrentAddress(List<AddressData>? addresses) {
    if (addresses == null || addresses.isEmpty) return 'No address';
    final address = addresses.firstWhere(
      (address) => address.isCurrent == true,
      orElse: () => addresses.first,
    );
    currentAddressData = address;
    return address.addressText ?? 'No address';
  }

  @override
  void initState() {
    super.initState();
    _instructionsController = TextEditingController();
    context.read<MealplanBloc>().add(ListDropoffOptionEvent());
    context.read<MealplanBloc>().add(ListDeliveryTimeEvent());
    context.read<MealplanBloc>().add(MealPlanProgressEvent(widget.mealPlanId));
    context.read<AccountBloc>().add(ListAddressesEvent());
  }

  bool value = false;
  late double ten;
  late double twelve;
  late double forteen;
  late double sixteen;
  late double eighteen;
  late double twenty;
  late double twentyFour;
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final screenWidth = MediaQuery.of(context).size.width;

    ten = screenWidth * 0.02545;
    twelve = screenWidth * 0.03054;
    forteen = screenWidth * 0.035;
    sixteen = screenWidth * 0.04073;
    eighteen = screenWidth * 0.04582;
    twenty = screenWidth * 0.05091;
    twentyFour = screenWidth * 0.06109;
  }

  @override
  void dispose() {
    _instructionsController.dispose();
    super.dispose();
  }

  String _formatTime(String? time) {
    if (time == null) return '00:00 AM';
    final parts = time.split(':');
    if (parts.length < 2) return '00:00 AM';
    int hour = int.tryParse(parts[0]) ?? 0;
    String minute = parts[1];
    String period = hour >= 12 ? 'PM' : 'AM';
    if (hour > 12) hour -= 12;
    if (hour == 0) hour = 12;
    return '$hour:$minute $period';
  }

  Widget _buildLoadingShimmer(Size size) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        height: size.height * 0.025,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(size.width * 0.01),
        ),
      ),
    );
  }

  void _saveDeliveryDetails() {
    if (_selectedDropoffOption == null) {
      setState(() => _showDropoffError = true);
      return;
    }
    if (_selectedDeliveryTime == null) {
      setState(() => _showDeliveryTimeError = true);
      return;
    }
    final mealPlanId = mealPlanData?.id;
    if (mealPlanId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Missing meal plan ID'),
          backgroundColor: Color(0xFFE11900),
        ),
      );
      return;
    }
    final orderData = {
      "meal_plan_id": mealPlanId,
      "drop_off_option_id": _selectedDropoffOption?.id,
      "drop_off_instructions": _dropOffInstructions,
      "delivery_time_id": _selectedDeliveryTime?.id,
      "address_id": currentAddressData?.id,
    };
    context.read<MealplanBloc>().add(Step7MealPlanEvent(orderData));
  }

  Widget _buildDropoffOptionDropdown(Size size) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Drop-Off Options',
          style: TextStyle(
            fontFamily: 'Inter',
            fontSize: forteen,
            fontWeight: FontWeight.w500,
            color: const Color(0xFF1F2122),
          ),
        ),
        SizedBox(height: size.height * 0.015),
        Container(
          height: size.height * 0.05,
          padding: EdgeInsets.symmetric(horizontal: size.width * 0.045),
          decoration: BoxDecoration(
            border: Border.all(
              color: _showDropoffError ? Colors.red : const Color(0xFFE1E3E6),
            ),
            borderRadius: BorderRadius.circular(size.width * 0.0875),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<DropoffOptionItem>(
              value: _selectedDropoffOption,
              isExpanded: true,
              hint: Text(
                'Select drop-off option',
                style: TextStyle(
                  color:
                      _showDropoffError ? Colors.red : const Color(0xFF66696D),
                  fontSize: sixteen,
                ),
              ),
              icon: Icon(
                Icons.keyboard_arrow_down,
                color: const Color(0xFF1F2122),
                size: size.width * 0.06,
              ),
              items: _dropoffOptions
                  .map<DropdownMenuItem<DropoffOptionItem>>((item) {
                return DropdownMenuItem<DropoffOptionItem>(
                  value: item,
                  child: Text(
                    item.name ?? '',
                    style: TextStyle(
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w400,
                      fontSize: size.width * 0.04,
                      color: const Color(0xFF66696D),
                    ),
                  ),
                );
              }).toList(),
              onChanged: (DropoffOptionItem? newValue) {
                setState(() {
                  _selectedDropoffOption = newValue;
                  _showDropoffError = false;
                });
              },
            ),
          ),
        ),
        if (_showDropoffError)
          Padding(
            padding: EdgeInsets.only(top: size.height * 0.01),
            child: Text(
              'Please select a drop-off option',
              style: TextStyle(
                color: Colors.red,
                fontSize: size.width * 0.03,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildDeliveryTimeOptions(Size size) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Delivery Time',
              style: TextStyle(
                fontFamily: 'Inter',
                fontSize: sixteen,
                fontWeight: FontWeight.w500,
                color: const Color(0xFF1F2122),
              ),
            ),
            Text(
              '45–50 Minutes',
              style: TextStyle(
                fontFamily: 'Inter',
                fontSize: sixteen,
                fontWeight: FontWeight.w500,
                color: const Color(0xFF1F2122),
              ),
            ),
          ],
        ),
        SizedBox(height: size.height * 0.015),
        ..._deliveryTimeOptions.map((option) => Padding(
              padding: EdgeInsets.only(bottom: size.height * 0.01),
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(
                    color: _selectedDeliveryTime?.id == option.id
                        ? const Color(0xFF1F2122)
                        : _showDeliveryTimeError
                            ? Colors.red
                            : const Color(0xFFE1E3E6),
                  ),
                  color: _selectedDeliveryTime?.id == option.id
                      ? const Color(0xFFE1DDD5)
                      : Colors.white,
                  borderRadius: BorderRadius.circular(size.width * 0.02),
                ),
                child: RadioListTile<DeliveryTimeItem>(
                  value: option,
                  groupValue: _selectedDeliveryTime,
                  onChanged: (DeliveryTimeItem? value) {
                    setState(() {
                      _selectedDeliveryTime = value;
                      _showDeliveryTimeError = false;
                    });
                  },
                  title: Text(
                    option.name ?? '',
                    style: TextStyle(
                        fontSize: forteen, fontWeight: FontWeight.w600),
                  ),
                  subtitle: Text(
                    option.description ?? '',
                    style: TextStyle(
                        fontSize: twelve, fontWeight: FontWeight.w400),
                  ),
                  secondary: Text(
                    '+\$${option.cost ?? '0.00'}',
                    style: TextStyle(
                        fontSize: forteen, fontWeight: FontWeight.w600),
                  ),
                ),
              ),
            )),
        if (_showDeliveryTimeError)
          Padding(
            padding: EdgeInsets.only(top: size.height * 0.01),
            child: Text(
              'Please select a delivery time',
              style: TextStyle(
                color: Colors.red,
                fontSize: size.width * 0.03,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildPlaceOrderButton(Size size) {
    return SizedBox(
      width: double.infinity,
      height: twentyFour + twenty,
      child: ElevatedButton(
        onPressed: _isPlacingOrder ? null : _saveDeliveryDetails,
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF1F2122),
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(size.width * 0.07),
          ),
          padding: EdgeInsets.symmetric(
            horizontal: _isPlacingOrder ? size.width * 0.08 : size.width * 0.05,
            vertical: size.height * 0.01,
          ),
        ),
        child: _isPlacingOrder
            ? SizedBox(
                width: size.width * 0.06,
                height: size.width * 0.06,
                child: const CircularProgressIndicator(
                  color: Colors.white,
                  strokeWidth: 2,
                ),
              )
            : Text(
                'Place Order',
                style: TextStyle(
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w400,
                  fontSize: sixteen,
                  color: Colors.white,
                ),
              ),
      ),
    );
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: selectedDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 7)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: Color(0xFF1F2122),
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: Color(0xFF1F2122),
            ),
          ),
          child: child!,
        );
      },
    );
    if (picked != null && picked != selectedDate) {
      setState(() {
        selectedDate = picked;
      });
    }
  }

  String _formatDisplayDate(DateTime date) {
    final now = DateTime.now();
    if (date.year == now.year &&
        date.month == now.month &&
        date.day == now.day) {
      return 'Today';
    }
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  Widget _buildDeliveryTimeText(Size size) {
    final startTime = mealPlanData?.timeSlot?.startTime;
    final startDateStr = mealPlanData?.startDate;
    DateTime? displayDate =
        startDateStr != null ? DateTime.tryParse(startDateStr) : selectedDate;

    return Row(
      children: [
        GestureDetector(
          onTap: () => _selectDate(context),
          child: Text(
            //'${_formatDisplayDate(displayDate ?? DateTime.now())}, ${_formatTime(startTime)}',
            "Today, 8:30AM",
            style: TextStyle(
              fontFamily: 'Inter',
              fontSize: forteen,
              fontWeight: FontWeight.w500,
              color: const Color(0xFF1F2122),
            ),
          ),
        ),
        // SizedBox(width: size.width * 0.01),
        // Image.asset(
        //   'assets/icons/chevron-down.png',
        //   width: size.width * 0.0375,
        //   height: size.width * 0.0375,
        //   color: const Color(0xFF1F2122),
        // ),
        // TextButton(
        //   onPressed: () => _selectDate(context),
        //   style: TextButton.styleFrom(
        //     padding: EdgeInsets.zero,
        //     minimumSize: Size(size.width * 0.075, size.height * 0.025),
        //     tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        //   ),
        //   child: Text(
        //     'Edit',
        //     style: TextStyle(
        //       fontFamily: 'Inter',
        //       fontWeight: FontWeight.w600,
        //       fontSize: size.width * 0.03,
        //       color: const Color(0xFF1F2122),
        //       decoration: TextDecoration.underline,
        //       decorationThickness: 1.5,
        //     ),
        //   ),
        // ),
      ],
    );
  }

  void _handleEditDay(int dayId, String date) {
    context.read<MealplanBloc>().add(ViewDayEvent(dayId));

    final mealSelectionType = mealPlanData?.mealSelectionType;

    if (mealSelectionType == "CURATED") {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => SelectChef(
            mealPlanId: widget.mealPlanId,
            dayId: dayId,
            date: date,
            isEditing: true,
          ),
        ),
      );
    } else {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => PersonailizedSelectchef(
            mealPlanId: widget.mealPlanId,
            currentday: dayId,
            dataresponce: {
              'start_date': mealPlanData?.startDate,
              'end_date': mealPlanData?.endDate,
              'serving_size_id': mealPlanData?.servingSizeId,
              'time_slot_id': mealPlanData?.timeSlotId,
              'meal_plan_duration': mealPlanData?.mealPlanDuration,
            },
            timeSlots: mealPlanData?.timeSlotId.toString(),
            isEditing: true,
            editDayId: dayId,
            editDate: date,
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isTablet = size.width >= 600;
    final itemSpacing = size.height * 0.02;
    return MultiBlocListener(
      listeners: [
        BlocListener<AccountBloc, AccountState>(
          listener: (context, state) {
            if (state is ListAddressesSuccess) {
              setState(() {
                currentAddress = _getCurrentAddress(state.data);
              });
            }
          },
        ),
        BlocListener<MealplanBloc, MealPlanState>(
          listener: (context, state) {
            if (state is MealPlanProgressSuccess) {
              setState(() {
                mealPlanData = state.data;
                _dropOffInstructions = _instructionsController.text;
              });
            } else if (state is ListDropoffOptionSuccess) {
              final dropoffData = state.data as DropoffOptionsModel;
              setState(() {
                _dropoffOptions = dropoffData.data?.data ?? [];
              });
            } else if (state is ListDeliveryTimeSuccess) {
              final deliveryTimeData = state.data as DeliveryTimeModel;
              setState(() {
                _deliveryTimeOptions = deliveryTimeData.data?.data ?? [];
              });
            } else if (state is Step7MealPlanLoading) {
              setState(() {
                _isPlacingOrder = true;
              });
            } else if (state is Step7MealPlanSuccess) {
              setState(() {
                _isPlacingOrder = false;
              });
              Navigator.pushAndRemoveUntil(
                context,
                MaterialPageRoute(
                    builder: (context) => const MainNavigationScreen()),
                (route) => false,
              );
            } else if (state is Step7MealPlanFailed) {
              setState(() {
                _isPlacingOrder = false;
              });
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message),
                  backgroundColor: const Color(0xFFE11900),
                ),
              );
            }
          },
        ),
      ],
      child: BlocBuilder<MealplanBloc, MealPlanState>(
        builder: (context, state) {
          return SafeArea(
            child: Scaffold(
              backgroundColor: const Color(0xFFF6F3EC),
              appBar: AppBar(
                backgroundColor: const Color(0xFFF6F3EC),
                scrolledUnderElevation: 0,
                surfaceTintColor: Colors.transparent,
                elevation: 0,
                automaticallyImplyLeading: false,
                leading: IconButton(
                  icon: Icon(
                    Icons.arrow_back,
                    color: const Color(0xFF1F2122),
                    size: size.width * 0.06,
                  ),
                  onPressed: () => Navigator.of(context).pop(),
                ),
                title: Text(
                  'Checkout',
                  style: TextStyle(
                    color: const Color(0xFF1F2122),
                    fontSize: isTablet ? size.width * 0.045 : size.width * 0.05,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'Inter',
                  ),
                ),
              ),
              body: SingleChildScrollView(
                padding: EdgeInsets.only(
                    top: ten, left: sixteen, right: sixteen, bottom: sixteen),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Delivery Details Card
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(size.width * 0.025),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.05),
                            blurRadius: size.width * 0.01,
                            offset: const Offset(0, 1),
                          ),
                        ],
                      ),
                      padding: EdgeInsets.all(size.width * 0.04),
                      child: state is MealPlanProgressLoading
                          ? Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: List.generate(
                                5,
                                (index) => Padding(
                                  padding: EdgeInsets.symmetric(
                                      vertical: size.height * 0.01),
                                  child: _buildLoadingShimmer(size),
                                ),
                              ),
                            )
                          : Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Delivery Details',
                                  style: TextStyle(
                                    fontFamily: 'Inter',
                                    fontSize: twenty,
                                    fontWeight: FontWeight.w600,
                                    color: const Color(0xFF1F2122),
                                  ),
                                ),
                                SizedBox(height: size.height * 0.02),
                                Text(
                                  'Address',
                                  style: TextStyle(
                                    fontFamily: 'Inter',
                                    fontSize: forteen,
                                    fontWeight: FontWeight.w500,
                                    color: const Color(0xFF1F2122),
                                  ),
                                ),
                                SizedBox(height: size.height * 0.01875),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Row(
                                      children: [
                                        Icon(
                                          Icons.location_on_outlined,
                                          size: sixteen,
                                          color: const Color(0xFF414346),
                                        ),
                                        SizedBox(width: size.width * 0.02),
                                        Text(
                                          currentAddress ?? 'No address',
                                          style: TextStyle(
                                            fontFamily: 'Inter',
                                            fontSize: forteen,
                                            fontWeight: FontWeight.w500,
                                            color: const Color(0xFF1F2122),
                                          ),
                                        ),
                                      ],
                                    ),
                                    TextButton(
                                        onPressed: () {
                                          Navigator.push(
                                            context,
                                            MaterialPageRoute(
                                              builder: (context) =>
                                                  EditAddressPage(
                                                currentAddress: currentAddress,
                                              ),
                                            ),
                                          );
                                        },
                                        style: TextButton.styleFrom(
                                          padding: EdgeInsets.zero,
                                          minimumSize: Size(size.width * 0.075,
                                              size.height * 0.025),
                                          tapTargetSize:
                                              MaterialTapTargetSize.shrinkWrap,
                                        ),
                                        child: IntrinsicWidth(
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              Text(
                                                'Edit',
                                                style: TextStyle(
                                                  fontFamily: 'Inter',
                                                  fontWeight: FontWeight.w600,
                                                  fontSize: twelve,
                                                  height:
                                                      1.4, // reduce line height
                                                  color:
                                                      const Color(0xFF1F2122),
                                                  decoration:
                                                      TextDecoration.none,
                                                ),
                                              ),
                                              Container(
                                                height: 1.5,
                                                width: double
                                                    .infinity, // Will match the text width exactly
                                                color: const Color(0xFF1F2122),
                                              ),
                                            ],
                                          ),
                                        )),
                                  ],
                                ),
                                SizedBox(height: size.height * 0.02),
                                Text(
                                  'Delivery',
                                  style: TextStyle(
                                    fontFamily: 'Inter',
                                    fontSize: forteen,
                                    fontWeight: FontWeight.w500,
                                    color: const Color(0xFF1F2122),
                                  ),
                                ),
                                SizedBox(height: size.height * 0.01875),
                                Row(
                                  children: [
                                    Icon(
                                      Icons.access_time_rounded,
                                      size: forteen,
                                      color: const Color(0xFF1F2122),
                                    ),
                                    SizedBox(width: size.width * 0.02),
                                    _buildDeliveryTimeText(size),
                                  ],
                                ),
                                SizedBox(height: size.height * 0.01),
                                Divider(
                                  color: const Color(0xFFE1E3E6),
                                  thickness: 1,
                                ),
                                SizedBox(height: size.height * 0.01),
                                _buildDropoffOptionDropdown(size),
                                SizedBox(height: size.height * 0.02),
                                Text(
                                  'Drop-Off Instructions',
                                  style: TextStyle(
                                    fontFamily: 'Inter',
                                    fontSize: forteen,
                                    fontWeight: FontWeight.w500,
                                    color: const Color(0xFF1F2122),
                                  ),
                                ),
                                SizedBox(height: size.height * 0.01),
                                TextField(
                                  controller: _instructionsController,
                                  onChanged: (value) {
                                    _dropOffInstructions = value;
                                  },
                                  textAlign: TextAlign.left,
                                  decoration: InputDecoration(
                                    contentPadding:
                                        EdgeInsets.all(size.width * 0.03),
                                    hintText:
                                        'Example: Doorbell is broken, please knock',
                                    hintStyle: TextStyle(
                                      fontFamily: 'Inter',
                                      fontWeight: FontWeight.w400,
                                      fontSize: sixteen,
                                      color: const Color(0xFF66696D),
                                    ),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(
                                          size.width * 0.02),
                                      borderSide: const BorderSide(
                                          color: Color(0xFFE1E3E6)),
                                    ),
                                    enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(
                                          size.width * 0.02),
                                      borderSide: const BorderSide(
                                          color: Color(0xFFE1E3E6)),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(
                                          size.width * 0.02),
                                      borderSide: const BorderSide(
                                          color: Color(0xFF1F2122), width: 1.5),
                                    ),
                                  ),
                                  minLines: 5,
                                  maxLines: 8,
                                  cursorColor: const Color(0xFF1F2122),
                                  style: TextStyle(
                                    fontFamily: 'Inter',
                                    fontWeight: FontWeight.w400,
                                    fontSize: size.width * 0.04,
                                    color: const Color(0xFF66696D),
                                  ),
                                ),
                                SizedBox(height: size.height * 0.02),
                                Divider(
                                  color: const Color(0xFFE1E3E6),
                                  thickness: 1,
                                ),
                                SizedBox(height: size.height * 0.02),
                                _buildDeliveryTimeOptions(size),
                              ],
                            ),
                    ),
                    SizedBox(height: size.height * 0.03),
                    // Payment Card
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(size.width * 0.025),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.05),
                            blurRadius: size.width * 0.01,
                            offset: const Offset(0, 1),
                          ),
                        ],
                      ),
                      padding: EdgeInsets.only(
                          top: twelve,
                          bottom: sixteen,
                          left: sixteen,
                          right: sixteen),
                      child: state is MealPlanProgressLoading
                          ? Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: List.generate(
                                4,
                                (index) => Padding(
                                  padding: EdgeInsets.symmetric(
                                      vertical: size.height * 0.01),
                                  child: _buildLoadingShimmer(size),
                                ),
                              ),
                            )
                          : Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Payment',
                                  style: TextStyle(
                                    fontFamily: 'Inter',
                                    fontSize: twenty,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.black,
                                  ),
                                ),
                                SizedBox(height: size.height * 0.03),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        // Visa Icon
                                        Container(
                                          width: size.width * 0.075,
                                          height: size.height * 0.025,
                                          decoration: BoxDecoration(
                                            borderRadius: BorderRadius.circular(
                                                size.width * 0.01),
                                          ),
                                          child: Image.asset(
                                            'assets/icons/Visa.png',
                                            fit: BoxFit.contain,
                                          ),
                                        ),
                                        SizedBox(width: size.width * 0.02),

                                        // Card Details
                                        Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            // Top Row: Card ending + Default aligned with space between
                                            Row(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                // Card Text
                                                Text(
                                                  'Card ending with \n0001',
                                                  style: TextStyle(
                                                    fontFamily: 'Inter',
                                                    fontSize: forteen,
                                                    fontWeight: FontWeight.w400,
                                                    color:
                                                        const Color(0xFF1F2122),
                                                  ),
                                                ),
                                                SizedBox(
                                                    width: size.width * 0.02),
                                                // Default Pill
                                                Container(
                                                  padding: EdgeInsets.symmetric(
                                                    horizontal:
                                                        size.width * 0.02,
                                                  ),
                                                  decoration: BoxDecoration(
                                                    color:
                                                        const Color(0xFFCEF8E0),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            size.width * 0.025),
                                                  ),
                                                  child: Text(
                                                    'Default',
                                                    style: TextStyle(
                                                      fontFamily: 'Inter',
                                                      fontSize: twelve,
                                                      fontWeight:
                                                          FontWeight.w500,
                                                      color: const Color(
                                                          0xFF007A4D),
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),

                                            SizedBox(height: twelve / 2),

                                            // Expiry
                                            Text(
                                              'Expires 03/2028',
                                              style: TextStyle(
                                                fontFamily: 'Inter',
                                                fontSize: forteen,
                                                fontWeight: FontWeight.w400,
                                                color: const Color(0xFF1F2122),
                                              ),
                                            ),
                                            SizedBox(height: eighteen / 2),
                                          ],
                                        ),
                                      ],
                                    ),

                                    // 3-dot menu icon
                                    IconButton(
                                      icon: Icon(
                                        Icons.more_vert,
                                        color: const Color(0xFF1F2122),
                                        size: size.width * 0.05,
                                      ),
                                      onPressed: () {},
                                      constraints: const BoxConstraints(),
                                      padding: EdgeInsets.zero,
                                    ),
                                  ],
                                ),
                                Divider(
                                  height: size.height * 0.01,
                                  color: const Color(0xFFE1E3E6),
                                ),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Row(
                                      children: [
                                        Container(
                                          width: size.width * 0.075,
                                          height: size.height * 0.025,
                                          decoration: BoxDecoration(
                                            borderRadius: BorderRadius.circular(
                                                size.width * 0.01),
                                          ),
                                          child: Image.asset(
                                            'assets/icons/db.png',
                                            fit: BoxFit.contain,
                                          ),
                                        ),
                                        SizedBox(width: size.width * 0.02),
                                        Text(
                                          '\$ 200.00',
                                          style: TextStyle(
                                            fontFamily: 'Inter',
                                            fontSize: forteen,
                                            fontWeight: FontWeight.w400,
                                            color: const Color(0xFF1F2122),
                                          ),
                                        ),
                                      ],
                                    ),
                                    IconButton(
                                      icon: Icon(
                                        Icons.more_vert,
                                        color: const Color(0xFF1F2122),
                                        size: size.width * 0.05,
                                      ),
                                      onPressed: () {},
                                      constraints: const BoxConstraints(),
                                      padding: EdgeInsets.zero,
                                    ),
                                  ],
                                ),
                                Divider(
                                  height: size.height * 0.01,
                                  color: const Color(0xFFE1E3E6),
                                ),
                                SizedBox(
                                  height: ten,
                                ),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  crossAxisAlignment: CrossAxisAlignment
                                      .start, // Align icon & column at top
                                  children: [
                                    Row(
                                      crossAxisAlignment: CrossAxisAlignment
                                          .start, // Ensures Visa aligns to top
                                      children: [
                                        // Visa icon
                                        Container(
                                          width: size.width * 0.075,
                                          height: size.height * 0.025,
                                          decoration: BoxDecoration(
                                            borderRadius: BorderRadius.circular(
                                                size.width * 0.01),
                                          ),
                                          child: Image.asset(
                                            'assets/icons/Visa.png',
                                            fit: BoxFit.contain,
                                          ),
                                        ),
                                        SizedBox(width: size.width * 0.02),

                                        // Card details
                                        Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              'Card ending with 0001',
                                              style: TextStyle(
                                                fontFamily: 'Inter',
                                                fontSize: forteen,
                                                fontWeight: FontWeight.w400,
                                                color: const Color(0xFF1F2122),
                                              ),
                                            ),
                                            SizedBox(height: twelve / 2),
                                            Text(
                                              'Expires 03/2028',
                                              style: TextStyle(
                                                fontFamily: 'Inter',
                                                fontSize: forteen,
                                                fontWeight: FontWeight.w400,
                                                color: const Color(0xFF1F2122),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),

                                    // More icon
                                    IconButton(
                                      icon: Icon(
                                        Icons.more_vert,
                                        color: const Color(0xFF1F2122),
                                        size: size.width * 0.05,
                                      ),
                                      onPressed: () {},
                                      constraints: const BoxConstraints(),
                                      padding: EdgeInsets.zero,
                                    ),
                                  ],
                                ),
                                Divider(
                                  height: size.height * 0.025,
                                  color: const Color(0xFFE1E3E6),
                                ),
                                SizedBox(height: size.height * 0.013),
                                TextButton.icon(
                                  onPressed: () {},
                                  icon: Icon(
                                    Icons.add,
                                    size: twentyFour,
                                    color: const Color(0xFF1F2122),
                                  ),
                                  label: IntrinsicWidth(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Text(
                                          'Add Payment Method',
                                          style: TextStyle(
                                            fontFamily: 'Inter',
                                            color: const Color(0xFF414346),
                                            fontSize: twelve,
                                            fontWeight: FontWeight.w600,
                                            height: 1.2,
                                            decoration: TextDecoration
                                                .none, // no default underline
                                          ),
                                        ),
                                        // SizedBox(height: 2), // controls how far the underline is
                                        Container(
                                          height: 1,
                                          width: double
                                              .infinity, // Will match the text width exactly
                                          color: const Color(0xFF414346),
                                        ),
                                      ],
                                    ),
                                  ),
                                  style: TextButton.styleFrom(
                                    padding: EdgeInsets.zero,
                                    minimumSize: Size(size.width * 0.075,
                                        size.height * 0.025),
                                    tapTargetSize:
                                        MaterialTapTargetSize.shrinkWrap,
                                    alignment: Alignment.centerLeft,
                                  ),
                                ),
                              ],
                            ),
                    ),
                    SizedBox(height: size.height * 0.03),
                    // Order Summary Card
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(size.width * 0.025),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.05),
                            blurRadius: size.width * 0.01,
                            offset: const Offset(0, 1),
                          ),
                        ],
                      ),
                      padding: EdgeInsets.all(size.width * 0.04),
                      child: state is MealPlanProgressLoading
                          ? Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: List.generate(
                                6,
                                (index) => Padding(
                                  padding: EdgeInsets.symmetric(
                                      vertical: size.height * 0.01),
                                  child: _buildLoadingShimmer(size),
                                ),
                              ),
                            )
                          : Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                  Text(
                                    'Order Summary',
                                    style: TextStyle(
                                      fontFamily: 'Inter',
                                      fontSize: eighteen,
                                      fontWeight: FontWeight.w600,
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                  SizedBox(height: size.height * 0.02),
                                  Row(
                                    children: List.generate(
                                      50,
                                      (index) => Container(
                                        width: size.width * 0.00625,
                                        height: 1,
                                        margin: EdgeInsets.symmetric(
                                            horizontal: size.width * 0.005),
                                        color: const Color(0xFFE1E3E6),
                                      ),
                                    ),
                                  ),
                                  SizedBox(height: size.height * 0.02),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        'Delivery Time Slot',
                                        style: TextStyle(
                                          fontFamily: 'Inter',
                                          fontSize: forteen,
                                          fontWeight: FontWeight.w500,
                                          color: const Color(0xFF1F2122),
                                        ),
                                      ),
                                      Row(
                                        children: [
                                          Icon(
                                            Icons.access_time_rounded,
                                            size: sixteen,
                                            color: const Color(0xFF1F2122),
                                          ),
                                          SizedBox(width: size.width * 0.01),
                                          Text(
                                            '${_formatTime(mealPlanData?.timeSlot?.startTime)} - ${_formatTime(mealPlanData?.timeSlot?.endTime)}',
                                            style: TextStyle(
                                              fontFamily: 'Inter',
                                              fontSize: forteen,
                                              fontWeight: FontWeight.w500,
                                              color: const Color(0xFF1F2122),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: size.height * 0.02),
                                  Row(
                                    children: List.generate(
                                      50,
                                      (index) => Container(
                                        width: size.width * 0.00625,
                                        height: 1,
                                        margin: EdgeInsets.symmetric(
                                            horizontal: size.width * 0.005),
                                        color: const Color(0xFFE1E3E6),
                                      ),
                                    ),
                                  ),
                                  SizedBox(height: size.height * 0.02),
                                  Text(
                                    'Order Details (${mealPlanData?.mealPlanDays?.length ?? 0} Days)',
                                    style: TextStyle(
                                      fontFamily: 'Inter',
                                      fontSize: forteen,
                                      fontWeight: FontWeight.w500,
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                  SizedBox(height: size.height * 0.02),
                                  ...(mealPlanData?.mealPlanDays
                                          ?.map((day) => _buildDailyOrderItem(
                                                date:
                                                    '${day.date}, ${day.dayOfWeek}',
                                                chef:
                                                    '${day.chef?.firstName ?? ''} ${day.chef?.lastName ?? ''}',
                                                dayTotal:
                                                    day.dayTotal ?? '0.00',
                                                chefImage:
                                                    day.chef?.profilePhoto,
                                                size: size,
                                                items: day.items,
                                                serves: mealPlanData
                                                    ?.servingSize?.serves,
                                              ))
                                          .toList() ??
                                      []),
                                  SizedBox(height: size.height * 0.02),
                                  Row(
                                    children: List.generate(
                                      50,
                                      (index) => Container(
                                        width: size.width * 0.00625,
                                        height: 1,
                                        margin: EdgeInsets.symmetric(
                                            horizontal: size.width * 0.005),
                                        color: const Color(0xFFE1E3E6),
                                      ),
                                    ),
                                  ),
                                  SizedBox(height: size.height * 0.02),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Row(
                                        children: [
                                          Icon(
                                            Icons.percent,
                                            size: twentyFour,
                                            color: const Color(0xFF1F2122),
                                          ),
                                          SizedBox(width: size.width * 0.01),
                                          Text(
                                            'Add promo code',
                                            style: TextStyle(
                                              fontFamily: 'Inter',
                                              fontSize: forteen,
                                              fontWeight: FontWeight.w500,
                                              color: const Color(0xFF1F2122),
                                            ),
                                          ),
                                        ],
                                      ),
                                      TextButton(
                                        onPressed: () {},
                                        style: TextButton.styleFrom(
                                          minimumSize: Size.zero,
                                          padding: EdgeInsets.zero,
                                          tapTargetSize:
                                              MaterialTapTargetSize.shrinkWrap,
                                        ),
                                        child: Row(
                                          children: [
                                            Icon(
                                              Icons.add,
                                              size: size.width * 0.0375,
                                              color: const Color(0xFF1F2122),
                                            ),
                                            SizedBox(width: size.width * 0.01),
                                            IntrinsicWidth(
                                              child: Column(
                                                children: [
                                                  Text(
                                                    'Add',
                                                    style: TextStyle(
                                                      fontFamily: 'Inter',
                                                      fontWeight:
                                                          FontWeight.w600,
                                                      fontSize:
                                                          size.width * 0.03,
                                                      color: const Color(
                                                          0xFF414346),
                                                    ),
                                                  ),
                                                  SizedBox(
                                                      height: size.height *
                                                          0.00125),
                                                  Container(
                                                    height: 1,
                                                    width: double
                                                        .infinity, // Will match the text width exactly
                                                    color:
                                                        const Color(0xFF1F2122),
                                                  ),
                                                ],
                                              ),
                                            )
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: size.height * 0.02),
                                  Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      // DB Icon aligned to top
                                      Padding(
                                        padding: EdgeInsets.only(
                                            top: size.height * 0.004),
                                        child: Container(
                                          width: size.width * 0.06,
                                          height: size.height * 0.02125,
                                          decoration: BoxDecoration(
                                            color: const Color(0xFF1F2122),
                                            borderRadius: BorderRadius.circular(
                                                size.width * 0.01),
                                          ),
                                          alignment: Alignment.center,
                                          child: Text(
                                            'DB',
                                            style: TextStyle(
                                              fontFamily: 'Inter',
                                              fontSize: size.width * 0.025,
                                              fontWeight: FontWeight.bold,
                                              color: Colors.white,
                                            ),
                                          ),
                                        ),
                                      ),

                                      SizedBox(width: size.width * 0.02),

                                      // Column with labels
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              'Dabba Wallet Credits',
                                              style: TextStyle(
                                                fontFamily: 'Inter',
                                                fontSize: forteen,
                                                fontWeight: FontWeight.w400,
                                                color: const Color(0xFF1F2122),
                                              ),
                                            ),
                                            Text(
                                              '\$3.50',
                                              style: TextStyle(
                                                fontFamily: 'Inter',
                                                fontSize: twelve,
                                                fontWeight: FontWeight.w400,
                                                color: const Color(0xFF1F2122),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),

                                      // Toggle aligned to right
                                      CustomToggle(
                                        value: value,
                                        onChanged: (bool newValue) {
                                          setState(() {
                                            value =
                                                newValue; // Update the actual variable
                                          });
                                        },
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: size.height * 0.02),
                                  Row(
                                    children: List.generate(
                                      50,
                                      (index) => Container(
                                        width: size.width * 0.00625,
                                        height: 1,
                                        margin: EdgeInsets.symmetric(
                                            horizontal: size.width * 0.005),
                                        color: const Color(0xFFE1E3E6),
                                      ),
                                    ),
                                  ),
                                  SizedBox(height: size.height * 0.018),
                                  Column(
                                    children: [
                                      Row(
                                        children: [
                                          Text(
                                            'Order Total',
                                            style: TextStyle(
                                              fontFamily: 'Inter',
                                              fontSize: sixteen,
                                              fontWeight: FontWeight.w500,
                                              color: const Color(0xFF1F2122),
                                            ),
                                          ),
                                        ],
                                      ),
                                      SizedBox(height: size.height * 0.014),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(
                                            'Subtotal',
                                            style: TextStyle(
                                              fontFamily: 'Inter',
                                              fontSize: forteen,
                                              fontWeight: FontWeight.w400,
                                              color: const Color(0xFF1F2122),
                                            ),
                                          ),
                                          Text(
                                            '\$${double.tryParse(mealPlanData?.subtotal ?? '0.00')?.toStringAsFixed(2) ?? '0.00'}',
                                            style: TextStyle(
                                              fontFamily: 'Inter',
                                              fontSize: twelve,
                                              fontWeight: FontWeight.w400,
                                              color: const Color(0xFF414346),
                                            ),
                                          ),
                                        ],
                                      ),
                                      SizedBox(height: size.height * 0.01),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(
                                            'Delivery fee',
                                            style: TextStyle(
                                              fontFamily: 'Inter',
                                              fontSize: forteen,
                                              fontWeight: FontWeight.w400,
                                              color: const Color(0xFF1F2122),
                                            ),
                                          ),
                                          Text(
                                            '\$${mealPlanData?.deliveryFee ?? '_'}',
                                            style: TextStyle(
                                              fontFamily: 'Inter',
                                              fontSize: twelve,
                                              fontWeight: FontWeight.w500,
                                              color: const Color(0xFF1F2122),
                                            ),
                                          ),
                                        ],
                                      ),
                                      SizedBox(height: size.height * 0.01),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(
                                            'Discounts',
                                            style: TextStyle(
                                              fontFamily: 'Inter',
                                              fontSize: forteen,
                                              fontWeight: FontWeight.w400,
                                              color: const Color(0xFF1F2122),
                                            ),
                                          ),
                                          Text(
                                            '-\$${mealPlanData?.discount ?? '_'}',
                                            style: TextStyle(
                                              fontFamily: 'Inter',
                                              fontSize: twelve,
                                              fontWeight: FontWeight.w500,
                                              color: const Color(0xFFD31510),
                                            ),
                                          ),
                                        ],
                                      ),
                                      SizedBox(height: size.height * 0.01),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(
                                            'DB Wallet Credits',
                                            style: TextStyle(
                                              fontFamily: 'Inter',
                                              fontSize: forteen,
                                              fontWeight: FontWeight.w400,
                                              color: const Color(0xFF1F2122),
                                            ),
                                          ),
                                          Text(
                                            '-\$${mealPlanData?.walletCredits ?? '_'}',
                                            style: TextStyle(
                                              fontFamily: 'Inter',
                                              fontSize: twelve,
                                              fontWeight: FontWeight.w500,
                                              color: const Color(0xFFD31510),
                                            ),
                                          ),
                                        ],
                                      ),
                                      SizedBox(height: size.height * 0.01),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(
                                            'Taxes & Fees',
                                            style: TextStyle(
                                              fontFamily: 'Inter',
                                              fontSize: forteen,
                                              fontWeight: FontWeight.w400,
                                              color: const Color(0xFF1F2122),
                                            ),
                                          ),
                                          Text(
                                            '\$${mealPlanData?.taxesAndFees ?? '_'}',
                                            style: TextStyle(
                                              fontFamily: 'Inter',
                                              fontSize: twelve,
                                              fontWeight: FontWeight.w500,
                                              color: const Color(0xFF1F2122),
                                            ),
                                          ),
                                        ],
                                      ),
                                      SizedBox(height: size.height * 0.005),
                                      Row(
                                        children: List.generate(
                                          50,
                                          (index) => Container(
                                            width: size.width * 0.00625,
                                            height: 1,
                                            margin: EdgeInsets.symmetric(
                                                horizontal: size.width * 0.005),
                                            color: const Color(0xFFE1E3E6),
                                          ),
                                        ),
                                      ),
                                      SizedBox(height: size.height * 0.01),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(
                                            'Total',
                                            style: TextStyle(
                                              fontFamily: 'Inter',
                                              fontSize: sixteen,
                                              fontWeight: FontWeight.w600,
                                              color: const Color(0xFF1F2122),
                                            ),
                                          ),
                                          Text(
                                            '\$${mealPlanData?.total ?? '_'}',
                                            style: TextStyle(
                                              fontFamily: 'Inter',
                                              fontSize: sixteen,
                                              fontWeight: FontWeight.w600,
                                              color: const Color(0xFF1F2122),
                                            ),
                                          ),
                                        ],
                                      ),
                                      SizedBox(height: size.height * 0.02),
                                      _buildPlaceOrderButton(size),
                                      SizedBox(height: size.height * 0.0225),
                                      Text(
                                        "By clicking the 'Place Order' button, a one-time payment for the 5-day meal plan will be charged.",
                                        textAlign: TextAlign.center,
                                        style: TextStyle(
                                          fontFamily: 'Inter',
                                          fontSize: forteen,
                                          fontWeight: FontWeight.w400,
                                          color: const Color(0xFF66696D),
                                        ),
                                      ),
                                      SizedBox(height: size.height * 0.03125),
                                    ],
                                  ),
                                ]),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildDailyOrderItem({
    required String date,
    required String chef,
    required String dayTotal,
    String? chefImage,
    required Size size,
    required List<MealItem>? items,
    required int? serves,
  }) {
    // Original input
    String rawDate = date;

// Extract the date part
    String datePart = rawDate.split(',')[0]; // '2025-06-27'

// Parse into DateTime
    DateTime parsedDate = DateTime.parse(datePart);

// Format to "Jun 27, 2025, Fri"
    String formattedDate = DateFormat('MMM d, yyyy, E').format(parsedDate);
    // Calculate total quantity for the badge
    final totalQuantity =
        items?.fold<int>(0, (sum, item) => sum + (item.quantity ?? 0)) ?? 0;
    // Calculate total price from items
    final totalItemPrice = items?.fold<double>(
          0.0,
          (sum, item) =>
              sum +
              ((item.quantity ?? 0) *
                  (double.tryParse(item.price ?? '0.00') ?? 0.0)),
        ) ??
        0.0;

    // Extract only the date part (e.g., "2025-06-24" from "2025-06-24, TUESDAY")
    final dateOnly = date.split(',')[0].trim();

    return Container(
      margin: EdgeInsets.only(bottom: size.height * 0.015),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: size.height * 0.01),
          Text(
            formattedDate,
            style: TextStyle(
              fontFamily: 'Inter',
              fontSize: twelve,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF1F2122),
            ),
          ),
          SizedBox(height: size.height * 0.01),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Column(
                children: [
                  Container(
                    width: twentyFour,
                    height: twentyFour,
                    decoration: const BoxDecoration(
                      shape: BoxShape.circle,
                      color: Color(0xFFE1DDD5),
                    ),
                    child: ClipOval(
                      child: chefImage != null && chefImage.isNotEmpty
                          ? FadeInImage.assetNetwork(
                              placeholder: 'img/chef_placeholder.png',
                              image: '${ServerHelper.imageUrl}$chefImage',
                              fit: BoxFit.cover,
                              imageErrorBuilder: (context, error, stackTrace) {
                                return Image.asset(
                                  'img/chef_placeholder.png',
                                  fit: BoxFit.cover,
                                );
                              },
                            )
                          : Image.asset(
                              'img/chef_placeholder.png',
                              fit: BoxFit.cover,
                            ),
                    ),
                  ),
                  SizedBox(height: size.height * 0.01),
                  if (totalQuantity > 0)
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: size.width * 0.015,
                        vertical: size.height * 0.0025,
                      ),
                      decoration: BoxDecoration(
                        color: const Color(0xFFE1E3E6),
                        borderRadius: BorderRadius.circular(size.width * 0.02),
                      ),
                      child: Text(
                        '$totalQuantity×',
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontSize: forteen,
                          fontWeight: FontWeight.w500,
                          color: const Color(0xFF1F2122),
                        ),
                      ),
                    ),
                ],
              ),
              SizedBox(width: size.width * 0.025),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Chef $chef',
                      style: TextStyle(
                        fontFamily: 'Inter',
                        fontSize: sixteen,
                        fontWeight: FontWeight.w600,
                        color: const Color(0xFF1F2122),
                      ),
                    ),
                    SizedBox(height: size.height * 0.01),
                    Padding(
                      padding: EdgeInsets.only(left: size.width * 0.0225),
                      child: Text(
                        '${serves ?? 1} serving${(serves ?? 1) > 1 ? 's' : ''}',
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontSize: forteen,
                          fontWeight: FontWeight.w500,
                          color: const Color(0xFF1F2122),
                        ),
                      ),
                    ),
                    SizedBox(height: size.height * 0.01),
                    Padding(
                      padding: EdgeInsets.only(left: size.width * 0.0225),
                      child: TextButton(
                          onPressed: () {
                            // Get the day data based on meal selection type
                            dynamic dayData;

                            print('Edit button pressed for date: $dateOnly');
                            print(
                                'Meal selection type: ${mealPlanData?.mealSelectionType}');

                            if (mealPlanData?.mealSelectionType == "CURATED") {
                              // For curated meal plans, use mealPlanDays
                              print(
                                  'Looking in mealPlanDays: ${mealPlanData?.mealPlanDays?.length} days');
                              try {
                                dayData =
                                    mealPlanData?.mealPlanDays?.firstWhere(
                                  (day) => day.date == dateOnly,
                                );
                                print('Found curated day data: ${dayData?.id}');
                              } catch (e) {
                                print('Error finding curated day: $e');
                                dayData = null;
                              }
                            } else {
                              // For personalized meal plans, use personalizedDays
                              print(
                                  'Looking in personalizedDays: ${mealPlanData?.personalizedDays?.length} days');
                              try {
                                dayData =
                                    mealPlanData?.personalizedDays?.firstWhere(
                                  (day) => day.date == dateOnly,
                                );
                                print(
                                    'Found personalized day data: ${dayData?.id}');
                              } catch (e) {
                                print('Error finding personalized day: $e');
                                dayData = null;
                              }
                            }

                            if (dayData?.id != null) {
                              print(
                                  'Calling _handleEditDay with dayId: ${dayData!.id!}');
                              _handleEditDay(dayData!.id!, date);
                            } else {
                              print('Day ID not found for date: $dateOnly');
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text('Day ID not found'),
                                  backgroundColor: Color(0xFFE11900),
                                ),
                              );
                            }
                          },
                          style: TextButton.styleFrom(
                            minimumSize: Size.zero,
                            padding: EdgeInsets.zero,
                            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                          ),
                          child: IntrinsicWidth(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  'Edit',
                                  style: TextStyle(
                                      fontFamily: 'Inter',
                                      fontSize: twelve,
                                      fontWeight: FontWeight.w600,
                                      color: const Color(0xFF414346),
                                      decoration: TextDecoration
                                          .none, // remove default underline
                                      height: 1.2),
                                ),
                                //   SizedBox(height: 2), // space between text and underline
                                Container(
                                  height: 1,
                                  width: double
                                      .infinity, // Will match the text width exactly
                                  color: const Color(0xFF414346),
                                ),
                              ],
                            ),
                          )),
                    ),
                  ],
                ),
              ),
              Text(
                '\$${totalItemPrice.toStringAsFixed(2)}',
                style: TextStyle(
                  fontFamily: 'Inter',
                  fontSize: forteen,
                  fontWeight: FontWeight.w500,
                  color: const Color(0xFF1F2122),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
