import 'package:db_eats/common/initializer.dart';
import 'package:db_eats/data/models/meal_plan/cuisinelistingmodel.dart';
import 'package:db_eats/data/models/meal_plan/filterchefsmodel.dart';
import 'package:db_eats/data/models/meal_plan/listdiatarymodel.dart';
import 'package:db_eats/data/models/meal_plan/spicelevellistmodel.dart';
import 'package:db_eats/server/serverhelper.dart';
import 'package:db_eats/ui/meal_plan/meal_plan.dart';
import 'package:db_eats/ui/meal_plan/view_chefmenu.dart';
import 'package:db_eats/ui/meal_plan/checkout_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:db_eats/bloc/mealplan_bloc.dart';
import 'package:shimmer/shimmer.dart';
import 'package:db_eats/data/models/meal_plan/mealplanprogressmodel.dart'
    as progress;
import 'package:db_eats/data/models/meal_plan/filterchefsmodel.dart' as filter;

// SCREEN 1: Choose Cuisines
class ChooseCuisines extends StatefulWidget {
  final int mealPlanId;
  const ChooseCuisines({super.key, required this.mealPlanId});

  @override
  State<ChooseCuisines> createState() => _ChooseCuisinesState();
}

class _ChooseCuisinesState extends State<ChooseCuisines> {
  final Set<int> _selectedCuisineIds = {};
  final Set<int> _selectedSubCuisineIds = {};
  final Set<int> _selectedLocalCuisineIds = {};

  Map<int, bool> _expandedCuisines = {};
  Map<int, bool> _expandedSubCuisines = {};
  bool _selectAll = false;
  late final MealplanBloc _mealPlanBloc;
  List<Cuisines> cuisines = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _mealPlanBloc = BlocProvider.of<MealplanBloc>(context);
    _mealPlanBloc.add(ListCuisineEvent());
  }

  late double ten;
  late double twelve;
  late double forteen;
  late double sixteen;
  late double eighteen;
  late double twenty;
  late double twentyFour;
  late double screenWidth;
  late double screenHeight;
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    screenWidth = MediaQuery.of(context).size.width;
    screenHeight = MediaQuery.of(context).size.height;

    ten = screenWidth * 0.02545;
    twelve = screenWidth * 0.03054;
    forteen = screenWidth * 0.035;
    sixteen = screenWidth * 0.04073;
    eighteen = screenWidth * 0.04582;
    twenty = screenWidth * 0.05091;
    twentyFour = screenWidth * 0.06109;
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    return BlocListener<MealplanBloc, MealPlanState>(
      listener: (context, state) {
        if (state is ListCuisineSuccess) {
          final cuisineData = state.data as CuisinesListModel;
          setState(() {
            cuisines = cuisineData.data?.cuisines ?? [];
          });
        }
        if (state is Step2MealPlanLoading) {
          setState(() {
            _isLoading = true;
          });
        } else if (state is Step2MealPlanSuccess) {
          setState(() {
            _isLoading = false;
          });
          // Navigate after successful API response
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) =>
                  DietaryPreferences(mealPlanId: widget.mealPlanId),
            ),
          );
        } else if (state is Step2MealPlanFailed) {
          setState(() {
            _isLoading = false;
          });
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(state.message)),
          );
        }
      },
      child: Scaffold(
        backgroundColor: const Color(0xFFF6F3EC),
        appBar: AppBar(
          backgroundColor: const Color(0xFFF6F3EC),
          elevation: 0,
          scrolledUnderElevation: 0,
          surfaceTintColor: Colors.transparent,
          automaticallyImplyLeading: false,
          centerTitle: true,
          title: Image.asset(
            'assets/logo.png',
            width: 112,
            height: 29,
            fit: BoxFit.contain,
          ),
          bottom: PreferredSize(
            preferredSize: Size.fromHeight(screenHeight * 0.002),
            child: Divider(
              color: Colors.grey[300],
              height: screenHeight * 0.002,
            ),
          ),
        ),
       body:
//         body: Column(
//   crossAxisAlignment: CrossAxisAlignment.start,
//   children: [
//     // Close button row
//     Padding(
//       padding: const EdgeInsets.fromLTRB(16, 0, 16, 24),
//       child: Row(
//         mainAxisAlignment: MainAxisAlignment.end,
//         children: [
//           InkWell(
//             onTap: () => Navigator.of(context).pop(),
//             child: const Icon(Icons.close, size: 22),
//           ),
//         ],
//       ),
//     ),
//     // Progress bar section
//     Padding(
//       padding: const EdgeInsets.symmetric(horizontal: 16.0),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           // Single continuous progress bar
//           Stack(
//             children: [
//               // Background bar (unfilled portion)
//               Container(
//                 height: 7,
//                 width: double.infinity,
//                 decoration: BoxDecoration(
//                   color: const Color(0xFFE1DDD5),
//                   borderRadius: BorderRadius.circular(4),
//                 ),
//               ),
//               // Progress bar (filled portion - 50% for second step)
//               FractionallySizedBox(
//                 widthFactor: 0.5, // 2/4 filled for second step
//                 child: Container(
//                   height: 7,
//                   decoration: BoxDecoration(
//                     color: const Color(0xFF007A4D),
//                     borderRadius: BorderRadius.circular(4),
//                   ),
//                 ),
//               ),
//             ],
//           ),
//           const SizedBox(height: 18),
//           // Step indicator text
//           Text(
//             '2 of 4',
//             style: TextStyle(
//               fontSize: forteen,
//               fontWeight: FontWeight.w400,
//               fontFamily: 'Inter',
//               color: Color(0xFF414346),
//             ),
//           ),
//         ],
//       ),
//     ),
//     const SizedBox(height: 16),
//     // Choose cuisines title - left aligned
//     Padding(
//       padding: EdgeInsets.symmetric(horizontal: 16.0),
//       child: Text(
//         "Choose cuisines",
//         style: TextStyle(
//           fontSize: twentyFour,
//           fontWeight: FontWeight.w600,
//           fontFamily: 'Inter',
//           color: Color(0xFF1F2122),
//         ),
//       ),
//     ),
//     const SizedBox(height: 16),
//     // Select All option with consistent padding
//     Padding(
//       padding: const EdgeInsets.symmetric(horizontal: 7.0),
//       child: Row(
//         children: [
//           Checkbox(
//             value: _selectAll,
//             onChanged: (bool? value) {
//               setState(() {
//                 _selectAll = value ?? false;
//                 if (_selectAll) {
//                   // Select all parent cuisines
//                   _selectedCuisineIds.addAll(cuisines
//                       .map((c) => c.id ?? 0)
//                       .where((id) => id != 0));

//                   // Select all sub cuisines
//                   for (var cuisine in cuisines) {
//                     if (cuisine.subCuisines != null) {
//                       _selectedSubCuisineIds.addAll(cuisine.subCuisines!
//                           .map((sc) => sc.id ?? 0)
//                           .where((id) => id != 0));

//                       // Select all local cuisines
//                       for (var subCuisine in cuisine.subCuisines!) {
//                         if (subCuisine.localCuisines != null) {
//                           _selectedLocalCuisineIds.addAll(subCuisine
//                               .localCuisines!
//                               .map((lc) => lc.id ?? 0)
//                               .where((id) => id != 0));
//                         }
//                       }
//                     }
//                   }
//                 } else {
//                   // Clear all selections
//                   _selectedCuisineIds.clear();
//                   _selectedSubCuisineIds.clear();
//                   _selectedLocalCuisineIds.clear();
//                 }
//               });
//             },
//             shape: RoundedRectangleBorder(
//               borderRadius: BorderRadius.circular(4),
//             ),
//             side: const BorderSide(color: Color(0xFF1F2122)),
//             activeColor: const Color(0xFF1F2122),
//           ),
//           Text(
//             "Select All",
//             style: TextStyle(
//               fontSize: forteen,
//               fontWeight: FontWeight.w400,
//               fontFamily: 'Inter',
//               color: const Color(0xFF1F2122),
//               height: 24 / 16,
//             ),
//           ),
//         ],
//       ),
//     ),
//     const SizedBox(height: 12),
//     // Divider below Select All
//     Padding(
//       padding: const EdgeInsets.symmetric(horizontal: 16.0),
//       child: Container(
//         height: 1,
//         color: const Color(0xFFE1DDD5),
//       ),
//     ),
//     // Scrollable content area
//     Expanded(
//       child: SingleChildScrollView(
//         child: Column(
//           children: [
//             // Cuisine list
//             Padding(
//               padding: const EdgeInsets.symmetric(horizontal: 16.0),
//               child: _buildTwoColumnCuisineList(),
//             ),
//             // const SizedBox(height: 50), // Space between list and buttons
//             SizedBox(height:  screenHeight * 0.225,),
//             // Button row - now inside scrollable area
//             Container(
//               width: double.infinity,
//               padding: const EdgeInsets.all(16.0),
//               decoration: BoxDecoration(
//                 color: const Color(0xFFF6F3EC),
//               ),
//               child: Row(
//                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                 children: [
//                   OutlinedButton(
//                     onPressed: () {
//                       Navigator.of(context).pop();
//                     },
//                     style: OutlinedButton.styleFrom(
//                       side: const BorderSide(color: Color(0xFF1F2122)),
//                       shape: RoundedRectangleBorder(
//                          borderRadius: BorderRadius.circular(screenWidth * 0.07),
//                       ),
//                        padding: EdgeInsets.symmetric(
//                 horizontal: screenWidth * 0.04, vertical: screenHeight * 0.02),
//             //  minimumSize: Size(screenWidth * 0.4, screenHeight * 0.06),
          
//                     ),
//                     child: const Text(
//                       'Back',
//                       style: TextStyle(
//                         fontFamily: 'Inter',
//                         fontWeight: FontWeight.w400,
//                         fontSize: 16,
//                         color: Color(0xFF1F2122),
//                       ),
//                     ),
//                   ),
//                   ElevatedButton(
//                     onPressed: _isLoading ? null : _onNextPressed,
//                     style: ElevatedButton.styleFrom(
//                       backgroundColor: Colors.black,
//                       foregroundColor: Colors.white,
//                       shape: RoundedRectangleBorder(
//                         borderRadius: BorderRadius.circular(screenWidth * 0.07),
//                       ),
//                       padding: EdgeInsets.symmetric(
//                 horizontal: screenWidth * 0.04, vertical: screenHeight * 0.02),
//             //  minimumSize: Size(screenWidth * 0.4, screenHeight * 0.06),
          
//                     ),
//                     child: _isLoading
//                         ? const SizedBox(
//                             width: 20,
//                             height: 20,
//                             child: CircularProgressIndicator(
//                               color: Colors.white,
//                               strokeWidth: 2,
//                             ),
//                           )
//                         : const Text(
//                             'Next',
//                             style: TextStyle(
//                               fontFamily: 'Inter',
//                               fontWeight: FontWeight.w400,
//                               fontSize: 16,
//                               color: Colors.white,
//                             ),
//                           ),
//                   ),
//                 ],
//               ),
//             ),
//             const SizedBox(height: 16), // Bottom padding
//           ],
//         ),
//       ),
//     ),
//   ],
// ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Close button row
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 24),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  InkWell(
                    onTap: () => Navigator.of(context).pop(),
                    child: const Icon(Icons.close, size: 22),
                  ),
                ],
              ),
            ),
            // Progress bar section
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Single continuous progress bar
                  Stack(
                    children: [
                      // Background bar (unfilled portion)
                      Container(
                        height: 7,
                        width: double.infinity,
                        decoration: BoxDecoration(
                          color: const Color(0xFFE1DDD5),
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                      // Progress bar (filled portion - 50% for second step)
                      FractionallySizedBox(
                        widthFactor: 0.5, // 2/4 filled for second step
                        child: Container(
                          height: 7,
                          decoration: BoxDecoration(
                            color: const Color(0xFF007A4D),
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 18),
                  // Step indicator text
                  Text(
                    '2 of 4',
                    style: TextStyle(
                      fontSize: forteen,
                      fontWeight: FontWeight.w400,
                      fontFamily: 'Inter',
                      color: Color(0xFF414346),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            // Choose cuisines title - left aligned
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.0),
              child: Text(
                "Choose cuisines",
                style: TextStyle(
                  fontSize: twentyFour,
                  fontWeight: FontWeight.w600,
                  fontFamily: 'Inter',
                  color: Color(0xFF1F2122),
                ),
              ),
            ),
            const SizedBox(height: 16),
            // Select All option with consistent padding
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 7.0),
              child: Row(
                children: [
                  Checkbox(
                    value: _selectAll,
                    onChanged: (bool? value) {
                      setState(() {
                        _selectAll = value ?? false;
                        if (_selectAll) {
                          // Select all parent cuisines
                          _selectedCuisineIds.addAll(cuisines
                              .map((c) => c.id ?? 0)
                              .where((id) => id != 0));

                          // Select all sub cuisines
                          for (var cuisine in cuisines) {
                            if (cuisine.subCuisines != null) {
                              _selectedSubCuisineIds.addAll(cuisine.subCuisines!
                                  .map((sc) => sc.id ?? 0)
                                  .where((id) => id != 0));

                              // Select all local cuisines
                              for (var subCuisine in cuisine.subCuisines!) {
                                if (subCuisine.localCuisines != null) {
                                  _selectedLocalCuisineIds.addAll(subCuisine
                                      .localCuisines!
                                      .map((lc) => lc.id ?? 0)
                                      .where((id) => id != 0));
                                }
                              }
                            }
                          }
                        } else {
                          // Clear all selections
                          _selectedCuisineIds.clear();
                          _selectedSubCuisineIds.clear();
                          _selectedLocalCuisineIds.clear();
                        }
                      });
                    },
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(4),
                    ),
                    side: const BorderSide(color: Color(0xFF1F2122)),
                    activeColor: const Color(0xFF1F2122),
                  ),
                  Text(
                    "Select All",
                    style: TextStyle(
                      fontSize: forteen,
                      fontWeight: FontWeight.w400,
                      fontFamily: 'Inter',
                      color: const Color(0xFF1F2122),
                      height: 24 / 16,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),
            // Divider below Select All
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Container(
                height: 1,
                color: const Color(0xFFE1DDD5),
              ),
            ),
            Expanded(
              child: SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: Column(
                    children: [
                      _buildTwoColumnCuisineList(),
                      const SizedBox(
                          height: 100), // Bottom padding for navigation buttons
                    ],
                  ),
                ),
              ),
            ),
            Container(
              padding: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                color: const Color(0xFFF6F3EC),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  OutlinedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    style: OutlinedButton.styleFrom(
                      side: const BorderSide(color: Color(0xFF1F2122)),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(28),
                      ),
                      padding: const EdgeInsets.symmetric(
                          horizontal: 20, vertical: 19),
                    ),
                    child: const Text(
                      'Back',
                      style: TextStyle(
                        fontFamily: 'Inter',
                        fontWeight: FontWeight.w400,
                        fontSize: 16,
                        color: Color(0xFF1F2122),
                      ),
                    ),
                  ),
                  ElevatedButton(
                    onPressed: _isLoading ? null : _onNextPressed,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.black,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(28),
                      ),
                      padding: EdgeInsets.symmetric(
                          horizontal: _isLoading ? 32 : 20, vertical: 19),
                    ),
                    child: _isLoading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              color: Colors.white,
                              strokeWidth: 2,
                            ),
                          )
                        : const Text(
                            'Next',
                            style: TextStyle(
                              fontFamily: 'Inter',
                              fontWeight: FontWeight.w400,
                              fontSize: 16,
                              color: Colors.white,
                            ),
                          ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // New method to build two-column cuisine list
  Widget _buildTwoColumnCuisineList() {
    List<Widget> cuisineBlocks = [];

    if (cuisines.isEmpty) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: 32.0),
          child: CircularProgressIndicator(color: Color(0xFF007A4D)),
        ),
      );
    }

    for (int i = 0; i < cuisines.length; i += 2) {
      List<Widget> rowItems = [];

      rowItems.add(
        Expanded(
          child: _buildExpandableCuisineColumn(cuisines[i]),
        ),
      );

      if (i + 1 < cuisines.length) {
        rowItems.add(const SizedBox(width: 8)); // Add spacing between columns
        rowItems.add(
          Expanded(
            child: _buildExpandableCuisineColumn(cuisines[i + 1]),
          ),
        );
      } else {
        // If odd number of cuisines, add an empty space for the second column
        rowItems.add(const SizedBox(width: 10));
        rowItems.add(Expanded(child: Container()));
      }

      // Add the row to our list of widgets
      cuisineBlocks.add(
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: rowItems,
        ),
      );
    }

    return Column(
      children: cuisineBlocks
          .map((block) => Padding(
                padding: const EdgeInsets.only(bottom: 0),
                child: block,
              ))
          .toList(),
    );
  }

  // Build a cuisine with its expandable sub-cuisines
  Widget _buildExpandableCuisineColumn(Cuisines cuisine) {
    final hasSubCuisines = cuisine.subCuisines?.isNotEmpty == true;
    final isExpanded = _expandedCuisines[cuisine.id ?? 0] ?? false;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Main cuisine item with checkbox and expansion capability
        _buildSimpleCuisineItem(cuisine),

        // Show sub-cuisines if expanded
        if (isExpanded && hasSubCuisines)
          Padding(
            padding: const EdgeInsets.only(left: 28.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: cuisine.subCuisines!.map((subCuisine) {
                final hasLocalCuisines =
                    subCuisine.localCuisines?.isNotEmpty == true;
                final isSubExpanded =
                    _expandedSubCuisines[subCuisine.id ?? 0] ?? false;

                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildSubCuisineItem(
                        subCuisine, hasLocalCuisines, isSubExpanded, cuisine),

                    // Show local cuisines if sub-cuisine is expanded
                    if (isSubExpanded && hasLocalCuisines)
                      Padding(
                        padding: const EdgeInsets.only(left: 28.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: subCuisine.localCuisines!
                              .map((local) => _buildLocalCuisineItem(
                                  local, subCuisine, cuisine))
                              .toList(),
                        ),
                      ),
                  ],
                );
              }).toList(),
            ),
          ),
      ],
    );
  }

  // Simple cuisine item for the two-column layout
  Widget _buildSimpleCuisineItem(Cuisines cuisine) {
    final hasSubCuisines = cuisine.subCuisines?.isNotEmpty == true;
    final isExpanded = _expandedCuisines[cuisine.id ?? 0] ?? false;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 1.0),
      child: Row(
        children: [
          Checkbox(
            value: _selectedCuisineIds.contains(cuisine.id),
            onChanged: (bool? value) {
              _handleParentCuisineSelection(cuisine, value);
            },
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(4),
            ),
            side: const BorderSide(color: Color(0xFF1F2122)),
            activeColor: const Color(0xFF1F2122),
          ),
          Expanded(
            child: Text(
              cuisine.name ?? '',
              style: TextStyle(
                fontSize: forteen,
                fontWeight: FontWeight.w400,
                color: Color(0xFF1F2122),
              ),
            ),
          ),
          // Only show expansion icon if there are sub-cuisines
          if (hasSubCuisines)
            InkWell(
              onTap: () {
                setState(() {
                  _expandedCuisines[cuisine.id ?? 0] = !isExpanded;
                });
              },
              child: Icon(
                isExpanded
                    ? Icons.keyboard_arrow_up
                    : Icons.keyboard_arrow_down,
                color: const Color(0xFF1F2122),
                size: 20,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildSubCuisineItem(SubCuisines subCuisine, bool hasLocalCuisines,
      bool isSubExpanded, Cuisines parentCuisine) {
    return InkWell(
      onTap: hasLocalCuisines
          ? () {
              setState(() {
                _expandedSubCuisines[subCuisine.id ?? 0] = !isSubExpanded;
              });
            }
          : null,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 1.0),
        child: Row(
          children: [
            Checkbox(
              value: _selectedSubCuisineIds.contains(subCuisine.id),
              onChanged: (bool? value) =>
                  _handleSubCuisineSelection(subCuisine, parentCuisine, value),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(4),
              ),
              side: const BorderSide(color: Color(0xFF1F2122)),
              activeColor: const Color(0xFF1F2122),
            ),
            Expanded(
              child: Text(
                subCuisine.name ?? '',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                  color: Color(0xFF1F2122),
                ),
              ),
            ),
            if (hasLocalCuisines)
              Icon(
                isSubExpanded
                    ? Icons.keyboard_arrow_up
                    : Icons.keyboard_arrow_down,
                color: const Color(0xFF1F2122),
                size: 20,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildLocalCuisineItem(LocalCuisines localCuisine,
      SubCuisines parentSubCuisine, Cuisines parentCuisine) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 1.0),
      child: Row(
        children: [
          Checkbox(
            value: _selectedLocalCuisineIds.contains(localCuisine.id),
            onChanged: (value) => _handleLocalCuisineSelection(
                localCuisine, parentSubCuisine, parentCuisine, value),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(4),
            ),
            side: const BorderSide(color: Color(0xFF1F2122)),
            activeColor: const Color(0xFF1F2122),
          ),
          Expanded(
            child: Text(
              localCuisine.name ?? '',
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: Color(0xFF1F2122),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _onNextPressed() {
    if (_selectedCuisineIds.isEmpty &&
        _selectedSubCuisineIds.isEmpty &&
        _selectedLocalCuisineIds.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select at least one cuisine'),
          backgroundColor: Color(0xFFE11900),
        ),
      );
      return;
    }

    final cuisineData = {
      "meal_plan_id": widget.mealPlanId,
      "cuisine_ids": _selectedCuisineIds.toList(),
      "subcuisine_ids": _selectedSubCuisineIds.toList(),
      "localcuisine_ids": _selectedLocalCuisineIds.toList(),
    };

    _mealPlanBloc.add(Step2MealPlanEvent(cuisineData));
  }

  void _handleLocalCuisineSelection(LocalCuisines localCuisine,
      SubCuisines parentSubCuisine, Cuisines parentCuisine, bool? selected) {
    setState(() {
      if (selected ?? false) {
        // Select local cuisine
        _selectedLocalCuisineIds.add(localCuisine.id ?? 0);
        // Select parent sub cuisine
        _selectedSubCuisineIds.add(parentSubCuisine.id ?? 0);
        // Select parent cuisine
        _selectedCuisineIds.add(parentCuisine.id ?? 0);
      } else {
        // Unselect local cuisine
        _selectedLocalCuisineIds.remove(localCuisine.id);
        // Unselect parent sub cuisine if no local cuisines are selected
        if (!parentSubCuisine.localCuisines!
            .any((lc) => _selectedLocalCuisineIds.contains(lc.id))) {
          _selectedSubCuisineIds.remove(parentSubCuisine.id);
        }
        // Unselect parent cuisine if no sub cuisines are selected
        if (!parentCuisine.subCuisines!
            .any((sc) => _selectedSubCuisineIds.contains(sc.id))) {
          _selectedCuisineIds.remove(parentCuisine.id);
        }
      }
    });
  }

  void _handleSubCuisineSelection(
      SubCuisines subCuisine, Cuisines parentCuisine, bool? selected) {
    setState(() {
      if (selected ?? false) {
        // Select sub cuisine
        _selectedSubCuisineIds.add(subCuisine.id ?? 0);
        // Select parent cuisine
        _selectedCuisineIds.add(parentCuisine.id ?? 0);
        // Select all local cuisines
        if (subCuisine.localCuisines != null) {
          _selectedLocalCuisineIds.addAll(subCuisine.localCuisines!
              .map((lc) => lc.id ?? 0)
              .where((id) => id != 0));
        }
      } else {
        // Unselect sub cuisine
        _selectedSubCuisineIds.remove(subCuisine.id);
        // Unselect all local cuisines
        if (subCuisine.localCuisines != null) {
          for (var lc in subCuisine.localCuisines!) {
            _selectedLocalCuisineIds.remove(lc.id);
          }
        }
        // Unselect parent cuisine if no sub cuisines are selected
        if (!parentCuisine.subCuisines!
            .any((sc) => _selectedSubCuisineIds.contains(sc.id))) {
          _selectedCuisineIds.remove(parentCuisine.id);
        }
      }
    });
  }

  void _handleParentCuisineSelection(Cuisines parentCuisine, bool? selected) {
    setState(() {
      if (selected ?? false) {
        // Select parent cuisine
        _selectedCuisineIds.add(parentCuisine.id ?? 0);

        // Select all sub cuisines
        if (parentCuisine.subCuisines != null) {
          for (var subCuisine in parentCuisine.subCuisines!) {
            _selectedSubCuisineIds.add(subCuisine.id ?? 0);

            // Select all local cuisines under each sub cuisine
            if (subCuisine.localCuisines != null) {
              _selectedLocalCuisineIds.addAll(subCuisine.localCuisines!
                  .map((lc) => lc.id ?? 0)
                  .where((id) => id != 0));
            }
          }
        }
      } else {
        // Unselect parent cuisine
        _selectedCuisineIds.remove(parentCuisine.id);

        // Unselect all sub cuisines
        if (parentCuisine.subCuisines != null) {
          for (var subCuisine in parentCuisine.subCuisines!) {
            _selectedSubCuisineIds.remove(subCuisine.id);

            // Unselect all local cuisines under each sub cuisine
            if (subCuisine.localCuisines != null) {
              for (var localCuisine in subCuisine.localCuisines!) {
                _selectedLocalCuisineIds.remove(localCuisine.id);
              }
            }
          }
        }
      }
    });
  }
}

class DietaryPreferences extends StatefulWidget {
  final int mealPlanId;
  const DietaryPreferences({super.key, required this.mealPlanId});

  @override
  State<DietaryPreferences> createState() => _DietaryPreferencesState();
}

class _DietaryPreferencesState extends State<DietaryPreferences> {
  int? _selectedPreferenceId;
  List<Dietaries> _preferences = [];
  late final MealplanBloc _mealPlanBloc;
  bool _isLoading = false;

  // Map dietary names to image assets
  final Map<String, String> _dietaryIcons = {
    'Organic': 'assets/icons/organic.png',
    'Halal': 'assets/icons/halal.png',
    'Vegan': 'assets/icons/vegan.png',
    'Vegetarian': 'assets/icons/veg.png',
    // Add more mappings as needed
  };

  @override
  void initState() {
    super.initState();
    _mealPlanBloc = BlocProvider.of<MealplanBloc>(context);
    _mealPlanBloc.add(ListDietaryEvent());
  }

  late double ten;
  late double twelve;
  late double forteen;
  late double sixteen;
  late double eighteen;
  late double twenty;
  late double twentyFour;
  late double screenWidth;
  late double screenHeight;
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    screenWidth = MediaQuery.of(context).size.width;
    screenHeight = MediaQuery.of(context).size.height;

    ten = screenWidth * 0.02545;
    twelve = screenWidth * 0.03054;
    forteen = screenWidth * 0.035;
    sixteen = screenWidth * 0.04073;
    eighteen = screenWidth * 0.04582;
    twenty = screenWidth * 0.05091;
    twentyFour = screenWidth * 0.06109;
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final size = MediaQuery.of(context).size;
    final padding = MediaQuery.of(context).padding;
    final isTablet = size.width >= 600; // Define tablet breakpoint
    final isLandscape =
        MediaQuery.of(context).orientation == Orientation.landscape;

    return BlocListener<MealplanBloc, MealPlanState>(
      listener: (context, state) {
        if (state is ListDietaryLoading) {
          setState(() {
            _isLoading = true;
          });
        } else if (state is ListDietarySuccess) {
          final dietaryData = state.data as DietaryListModel;
          setState(() {
            _isLoading = false;
            _preferences = dietaryData.data?.dietaries ?? [];
          });
        } else if (state is ListDietaryFailed) {
          setState(() {
            _isLoading = false;
          });
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(state.message)),
          );
        } else if (state is Step3MealPlanLoading) {
          setState(() {
            _isLoading = true;
          });
        } else if (state is Step3MealPlanSuccess) {
          setState(() {
            _isLoading = false;
          });
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => SpiceLevel(mealPlanId: widget.mealPlanId),
            ),
          );
        } else if (state is Step3MealPlanFailed) {
          setState(() {
            _isLoading = false;
          });
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(state.message)),
          );
        }
      },
      child: SafeArea(
        child: Scaffold(
          backgroundColor: const Color(0xFFF6F3EC),
          appBar: AppBar(
            backgroundColor: const Color(0xFFF6F3EC),
            elevation: 0,
            scrolledUnderElevation: 0,
            surfaceTintColor: Colors.transparent,
            automaticallyImplyLeading: false,
            centerTitle: true,
            title: Image.asset(
              'assets/logo.png',
              width: 112,
              height: 29,
              fit: BoxFit.contain,
            ),
            bottom: PreferredSize(
              preferredSize: Size.fromHeight(screenHeight * 0.002),
              child: Divider(
                color: Colors.grey[300],
                height: screenHeight * 0.002,
              ),
            ),
          ),
          body: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: size.height * 0.02),
              // Close button and header section
              Padding(
                padding: EdgeInsets.fromLTRB(
                  size.width * 0.04,
                  0,
                  size.width * 0.04,
                  size.height * 0.03,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    InkWell(
                      onTap: () => Navigator.of(context).pop(),
                      child: Icon(
                        Icons.close,
                        size: size.width * 0.06,
                      ),
                    ),
                  ],
                ),
              ),
              // Progress bar section
              Padding(
                padding: EdgeInsets.symmetric(horizontal: size.width * 0.04),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Single continuous progress bar
                    Stack(
                      children: [
                        Container(
                          height: size.height * 0.01,
                          width: double.infinity,
                          decoration: BoxDecoration(
                            color: const Color(0xFFE1DDD5),
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                        FractionallySizedBox(
                          widthFactor: 0.75, // 3/4 filled for third step
                          child: Container(
                            height: size.height * 0.01,
                            decoration: BoxDecoration(
                              color: const Color(0xFF007A4D),
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: size.height * 0.02),
                    Text(
                      '3 of 4',
                      style: TextStyle(
                        fontSize: size.width * 0.035,
                        fontWeight: FontWeight.w400,
                        fontFamily: 'Inter',
                        color: const Color(0xFF414346),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: size.height * 0.02),
              // Title
              Padding(
                padding: EdgeInsets.symmetric(horizontal: size.width * 0.04),
                child: Text(
                  "Select your dietary preferences",
                  style: TextStyle(
                    fontSize: isTablet ? size.width * 0.05 : twentyFour,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'Inter',
                    color: const Color(0xFF1F2122),
                  ),
                ),
              ),
              SizedBox(height: size.height * 0.03),
              // List of preferences
              Flexible(
                child: ConstrainedBox(   constraints: BoxConstraints(
                      maxHeight: size.height * 0.4, // or any other max height you want
                    ),
                  child: _isLoading
                      ? const Center(
                          child: CircularProgressIndicator(
                            color: Color(0xFF007A4D),
                          ),
                        )
                      : isTablet
                          ? _buildGridView(size, isLandscape)
                          : _buildListView(size),
                ),
              ),
              // Navigation buttons
              Padding(
                padding: EdgeInsets.all(size.width * 0.04),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Flexible(
                      child: OutlinedButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                        style: OutlinedButton.styleFrom(
                          side: const BorderSide(color: Color(0xFF1F2122)),
                          shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(screenWidth * 0.07),
                      ),
                       padding: EdgeInsets.symmetric(
                horizontal: screenWidth * 0.04, vertical: screenHeight * 0.02),
                        ),
                        child: Text(
                          'Back',
                          style: TextStyle(
                            fontFamily: 'Inter',
                            fontWeight: FontWeight.w400,
                            fontSize: size.width * 0.04,
                            color: const Color(0xFF1F2122),
                          ),
                        ),
                      ),
                    ),
                    SizedBox(width: size.width * 0.02),
                    Flexible(
                      child: ElevatedButton(
                        onPressed: _isLoading
                            ? null
                            : () {
                                if (_selectedPreferenceId == null) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text(
                                          'Please select a dietary preference'),
                                      backgroundColor: Color(0xFFE11900),
                                    ),
                                  );
                                  return;
                                }

                                final dietaryData = {
                                  "meal_plan_id": widget.mealPlanId,
                                  "dietary_preference_id":
                                      _selectedPreferenceId,
                                };

                                _mealPlanBloc
                                    .add(Step3MealPlanEvent(dietaryData));
                              },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.black,
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(screenWidth * 0.07),
                      ),
                       padding: EdgeInsets.symmetric(
                horizontal: screenWidth * 0.04, vertical: screenHeight * 0.02),
                        ),
                        child: _isLoading
                            ? SizedBox(
                                width: size.width * 0.05,
                                height: size.width * 0.05,
                                child: const CircularProgressIndicator(
                                  color: Colors.white,
                                  strokeWidth: 2,
                                ),
                              )
                            : Text(
                                'Next',
                                style: TextStyle(
                                  fontFamily: 'Inter',
                                  fontWeight: FontWeight.w400,
                                  fontSize: size.width * 0.04,
                                  color: Colors.white,
                                ),
                              ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildListView(Size size) {
    return ListView.builder(
      padding: EdgeInsets.symmetric(horizontal: size.width * 0.04),
      itemCount: _preferences.length,
      itemBuilder: (context, index) {
        final preference = _preferences[index];
        final isSelected = _selectedPreferenceId == preference.id;
        return Padding(
          padding: EdgeInsets.only(bottom: size.height * 0.015),
          child: InkWell(
            onTap: () {
              setState(() {
                _selectedPreferenceId = preference.id;
              });
            },
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: size.width * 0.04,
                vertical: size.height * 0.02,
              ),
              decoration: BoxDecoration(
                color: isSelected
                    ? const Color(0xFFE1DDD5)
                    : const Color(0xFFF6F3EC),
                borderRadius: BorderRadius.circular(size.width * 0.02),
                border: Border.all(
                  color: isSelected
                      ? const Color(0xFF1F2122)
                      : const Color(0xFFB9B6AD),
                  width: 1,
                ),
                boxShadow: isSelected
                    ? [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ]
                    : null,
              ),
              child: Row(
                children: [
                  Image.asset(
                    _dietaryIcons[preference.name] ??
                        'assets/icons/organic.png',
                    width: twentyFour,
                    height: twentyFour,
                    fit: BoxFit.contain,
                    errorBuilder: (context, error, stackTrace) => Icon(
                      Icons.error,
                      size: size.width * 0.06,
                      color: Colors.red,
                    ),
                  ),
                  SizedBox(width: size.width * 0.05),
                  Expanded(
                    child: Text(
                      preference.name ?? '',
                      style: TextStyle(
                        fontSize: forteen,
                        fontWeight: FontWeight.w600,
                        fontFamily: 'Inter',
                        color: const Color(0xFF1F2122),
                        height: 1.4,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildGridView(Size size, bool isLandscape) {
    return GridView.builder(
      padding: EdgeInsets.symmetric(horizontal: size.width * 0.04),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount:
            isLandscape ? 3 : 2, // 3 columns in landscape, 2 in portrait
        crossAxisSpacing: size.width * 0.02,
        mainAxisSpacing: size.height * 0.015,
        childAspectRatio: 3.0, // Adjust for content
      ),
      itemCount: _preferences.length,
      itemBuilder: (context, index) {
        final preference = _preferences[index];
        final isSelected = _selectedPreferenceId == preference.id;
        return InkWell(
          onTap: () {
            setState(() {
              _selectedPreferenceId = preference.id;
            });
          },
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: size.width * 0.04,
              vertical: size.height * 0.02,
            ),
            decoration: BoxDecoration(
              color: isSelected
                  ? const Color(0xFFE1DDD5)
                  : const Color(0xFFF6F3EC),
              borderRadius: BorderRadius.circular(size.width * 0.02),
              border: Border.all(
                color: isSelected
                    ? const Color(0xFF1F2122)
                    : const Color(0xFFB9B6AD),
                width: 1,
              ),
              boxShadow: isSelected
                  ? [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ]
                  : null,
            ),
            child: Row(
              children: [
                Image.asset(
                  _dietaryIcons[preference.name] ?? 'assets/icons/organic.png',
                  width: size.width * 0.05,
                  height: size.width * 0.05,
                  fit: BoxFit.contain,
                  errorBuilder: (context, error, stackTrace) => Icon(
                    Icons.error,
                    size: size.width * 0.05,
                    color: Colors.red,
                  ),
                ),
                SizedBox(width: size.width * 0.03),
                Expanded(
                  child: Text(
                    preference.name ?? '',
                    style: TextStyle(
                      fontSize: size.width * 0.035,
                      fontWeight: FontWeight.w600,
                      fontFamily: 'Inter',
                      color: const Color(0xFF1F2122),
                      height: 1.4,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

class SpiceLevel extends StatefulWidget {
  final int mealPlanId;
  const SpiceLevel({super.key, required this.mealPlanId});

  @override
  State<SpiceLevel> createState() => _SpiceLevelState();
}

class _SpiceLevelState extends State<SpiceLevel> {
  String _selectedSpiceLevel = 'No spice';
  int? _selectedSpiceLevelId;
  bool _isLoading = false;
  List<SpiceLevels> _spiceLevels = [];
  late final MealplanBloc _mealPlanBloc;

  @override
  void initState() {
    super.initState();
    _mealPlanBloc = BlocProvider.of<MealplanBloc>(context);
    _mealPlanBloc.add(ListSpiceLevelEvent());
  }

  late double ten;
  late double twelve;
  late double forteen;
  late double sixteen;
  late double eighteen;
  late double twenty;
  late double twentyFour;
  late double screenWidth;
  late double screenHeight;
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    screenWidth = MediaQuery.of(context).size.width;
    screenHeight = MediaQuery.of(context).size.height;

    ten = screenWidth * 0.02545;
    twelve = screenWidth * 0.03054;
    forteen = screenWidth * 0.035;
    sixteen = screenWidth * 0.04073;
    eighteen = screenWidth * 0.04582;
    twenty = screenWidth * 0.05091;
    twentyFour = screenWidth * 0.06109;
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final size = MediaQuery.of(context).size;
    final isTablet = size.width >= 600; // Define tablet breakpoint
    final isLandscape =
        MediaQuery.of(context).orientation == Orientation.landscape;

    return BlocListener<MealplanBloc, MealPlanState>(
      listener: (context, state) {
        if (state is ListSpiceLevelSuccess) {
          final spiceLevelData = state.data as SpiceLevelListModel;
          setState(() {
            _spiceLevels = spiceLevelData.data?.spiceLevels ?? [];
          });
        } else if (state is Step4MealPlanLoading) {
          setState(() {
            _isLoading = true;
          });
        } else if (state is Step4MealPlanSuccess) {
          setState(() {
            _isLoading = false;
          });
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => SelectChef(mealPlanId: widget.mealPlanId),
            ),
          );
        } else if (state is Step4MealPlanFailed) {
          setState(() {
            _isLoading = false;
          });
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(state.message)),
          );
        }
      },
      child: SafeArea(
        child: Scaffold(
          backgroundColor: const Color(0xFFF6F3EC),
          appBar: AppBar(
            backgroundColor: const Color(0xFFF6F3EC),
            elevation: 0,
            scrolledUnderElevation: 0,
            surfaceTintColor: Colors.transparent,
            automaticallyImplyLeading: false,
            centerTitle: true,
            title: Image.asset(
              'assets/logo.png',
              width: 112,
              height: 29,
              fit: BoxFit.contain,
            ),
            bottom: PreferredSize(
              preferredSize: Size.fromHeight(screenHeight * 0.002),
              child: Divider(
                color: Colors.grey[300],
                height: screenHeight * 0.002,
              ),
            ),
          ),
          body: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: size.height * 0.02),
              // Close button row
              Padding(
                padding: EdgeInsets.fromLTRB(
                  size.width * 0.04,
                  0,
                  size.width * 0.04,
                  size.height * 0.03,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    InkWell(
                      onTap: () => Navigator.of(context).pop(),
                      child: Icon(
                        Icons.close,
                        size: size.width * 0.06,
                      ),
                    ),
                  ],
                ),
              ),
              // Progress bar
              Padding(
                padding: EdgeInsets.symmetric(horizontal: size.width * 0.04),
                child: Stack(
                  children: [
                    Container(
                      height: size.height * 0.01,
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color: const Color(0xFFE1DDD5),
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                    FractionallySizedBox(
                      widthFactor: 1.0, // Fully filled for step 4
                      child: Container(
                        height: size.height * 0.01,
                        decoration: BoxDecoration(
                          color: const Color(0xFF007A4D),
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: size.height * 0.02),
              // Step indicator
              Padding(
                padding: EdgeInsets.symmetric(horizontal: size.width * 0.04),
                child: Text(
                  '4 of 4',
                  style: TextStyle(
                    fontSize: forteen,
                    color: Colors.grey[700],
                    fontWeight: FontWeight.w500,
                    fontFamily: 'Inter',
                  ),
                ),
              ),
              SizedBox(height: size.height * 0.02),
              // Title
              Padding(
                padding: EdgeInsets.symmetric(horizontal: size.width * 0.04),
                child: Text(
                  'Select your spice level',
                  style: TextStyle(
                    fontSize: isTablet ? size.width * 0.05 : twentyFour,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey[900],
                    fontFamily: 'Inter',
                  ),
                ),
              ),
              SizedBox(height: size.height * 0.03),
              // Spice level options
              Flexible(
                child: ConstrainedBox(  constraints: BoxConstraints(
                      maxHeight: size.height * 0.4, // or any other max height you want
                    ),
                  child: _spiceLevels.isEmpty
                      ? const Center(
                          child: CircularProgressIndicator(
                            color: Color(0xFF007A4D),
                          ),
                        )
                      : isTablet
                          ? _buildGridView(size, isLandscape)
                          : _buildListView(size),
                ),
              ),
              // Navigation buttons
              Padding(
                padding: EdgeInsets.all(size.width * 0.04),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Flexible(
                      child: OutlinedButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                        style: OutlinedButton.styleFrom(
                          side: const BorderSide(color: Color(0xFF1F2122)),
                          shape: RoundedRectangleBorder(
                           borderRadius: BorderRadius.circular(screenWidth * 0.07),
                      ),
                       padding: EdgeInsets.symmetric(
                horizontal: screenWidth * 0.04, vertical: screenHeight * 0.02),
                        ),
                        child: Text(
                          'Back',
                          style: TextStyle(
                            fontFamily: 'Inter',
                            fontWeight: FontWeight.w400,
                            fontSize: size.width * 0.04,
                            color: const Color(0xFF1F2122),
                          ),
                        ),
                      ),
                    ),
                    SizedBox(width: size.width * 0.02),
                    Flexible(
                      child: ElevatedButton(
                        onPressed: _isLoading
                            ? null
                            : () {
                                if (_selectedSpiceLevelId == null) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content:
                                          Text('Please select a spice level'),
                                      backgroundColor: Color(0xFFE11900),
                                    ),
                                  );
                                  return;
                                }

                                final spiceLevelData = {
                                  "meal_plan_id": widget.mealPlanId,
                                  "spice_level_id": _selectedSpiceLevelId,
                                };

                                _mealPlanBloc
                                    .add(Step4MealPlanEvent(spiceLevelData));
                              },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.black,
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(screenWidth * 0.07),
                      ),
                       padding: EdgeInsets.symmetric(
                horizontal: screenWidth * 0.04, vertical: screenHeight * 0.02),
                        ),
                        child: _isLoading
                            ? SizedBox(
                                width: size.width * 0.05,
                                height: size.width * 0.05,
                                child: const CircularProgressIndicator(
                                  color: Colors.white,
                                  strokeWidth: 2,
                                ),
                              )
                            : Text(
                                'Next',
                                style: TextStyle(
                                  fontFamily: 'Inter',
                                  fontWeight: FontWeight.w400,
                                  fontSize: size.width * 0.04,
                                  color: Colors.white,
                                ),
                              ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildListView(Size size) {
    return ListView.builder(
      padding: EdgeInsets.symmetric(horizontal: size.width * 0.04),
      itemCount: _spiceLevels.length,
      itemBuilder: (context, index) {
        final spiceLevel = _spiceLevels[index];
        final isSelected = _selectedSpiceLevelId == spiceLevel.id;
        final isNoSpice = spiceLevel.name?.toLowerCase() == 'no spice';

        return Container(
          margin: EdgeInsets.symmetric(vertical: size.height * 0.01),
          decoration: BoxDecoration(
            border: Border.all(
              color: isSelected ? Colors.black : const Color(0xFFB9B6AD),
              width: 1,
            ),
            borderRadius: BorderRadius.circular(size.width * 0.02),
            color:
                isSelected ? const Color(0xFFE1DDD5) : const Color(0xFFF6F3EC),
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(size.width * 0.02),
              onTap: () {
                setState(() {
                  _selectedSpiceLevelId = spiceLevel.id;
                  _selectedSpiceLevel = spiceLevel.name ?? '';
                });
              },
              child: Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: size.width * 0.04,
                  vertical: size.height * 0.02,
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        spiceLevel.name ?? '',
                        style: TextStyle(
                          fontSize: forteen,
                          fontWeight: FontWeight.w600,
                          fontFamily: 'Inter',
                          height: 1.4,
                          color: const Color(0xFF1F2122),
                        ),
                      ),
                    ),
                    if (isNoSpice)
                      Container(
                        width: size.width * 0.06,
                        height: size.width * 0.06,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: const Color(0xFF1F2122),
                            width: 1,
                          ),
                        ),
                        child: Center(
                          child: Icon(
                            Icons.close,
                            size: size.width * 0.04,
                            color: const Color(0xFF1F2122),
                          ),
                        ),
                      )
                    else
                      _buildSpiceIcons(index, size),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildGridView(Size size, bool isLandscape) {
    return GridView.builder(
      padding: EdgeInsets.symmetric(horizontal: size.width * 0.04),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount:
            isLandscape ? 3 : 2, // 3 columns in landscape, 2 in portrait
        crossAxisSpacing: size.width * 0.02,
        mainAxisSpacing: size.height * 0.015,
        childAspectRatio: 3.0, // Adjust for content
      ),
      itemCount: _spiceLevels.length,
      itemBuilder: (context, index) {
        final spiceLevel = _spiceLevels[index];
        final isSelected = _selectedSpiceLevelId == spiceLevel.id;
        final isNoSpice = spiceLevel.name?.toLowerCase() == 'no spice';

        return Container(
          margin: EdgeInsets.symmetric(vertical: size.height * 0.01),
          decoration: BoxDecoration(
            border: Border.all(
              color: isSelected ? Colors.black : const Color(0xFFB9B6AD),
              width: 1,
            ),
            borderRadius: BorderRadius.circular(size.width * 0.02),
            color:
                isSelected ? const Color(0xFFE1DDD5) : const Color(0xFFF6F3EC),
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(size.width * 0.02),
              onTap: () {
                setState(() {
                  _selectedSpiceLevelId = spiceLevel.id;
                  _selectedSpiceLevel = spiceLevel.name ?? '';
                });
              },
              child: Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: size.width * 0.04,
                  vertical: size.height * 0.02,
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        spiceLevel.name ?? '',
                        style: TextStyle(
                          fontSize: size.width * 0.035,
                          fontWeight: FontWeight.w600,
                          fontFamily: 'Inter',
                          height: 1.4,
                          color: const Color(0xFF1F2122),
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    if (isNoSpice)
                      Container(
                        width: size.width * 0.05,
                        height: size.width * 0.05,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: const Color(0xFF1F2122),
                            width: 1,
                          ),
                        ),
                        child: Center(
                          child: Icon(
                            Icons.close,
                            size: size.width * 0.035,
                            color: const Color(0xFF1F2122),
                          ),
                        ),
                      )
                    else
                      _buildSpiceIcons(index, size),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildSpiceIcons(int count, Size size) {
    return Row(
      children: List.generate(
        count,
        (index) => Padding(
          padding: EdgeInsets.only(right: size.width * 0.01),
          child: Image.asset(
            'assets/icons/spice.png',
            width: size.width * 0.06,
            height: size.width * 0.06,
            color: Colors.black,
            errorBuilder: (context, error, stackTrace) => Icon(
              Icons.error,
              size: size.width * 0.06,
              color: Colors.red,
            ),
          ),
        ),
      ),
    );
  }
}

class SelectChef extends StatefulWidget {
  final int mealPlanId;
  final int? dayId;
  final String? date;
  final bool isEditing;

  const SelectChef({
    super.key,
    required this.mealPlanId,
    this.dayId,
    this.date,
    this.isEditing = false,
  });

  @override
  State<SelectChef> createState() => _SelectChefState();
}

class _SelectChefState extends State<SelectChef> {
  List<Chefs> _chefs = [];
  int _selectedDate = 0;
  int _currentDay = 0;
  final Map<int, Map<String, dynamic>> _selectedChefs = {};
  late final MealplanBloc _mealPlanBloc;
  String _startDate = '';
  String _deliveryTime = '';
  bool _isLoading = true;
  List<String> _nextDatesFromStartDate = [];
  String _currentDateDisplay = '';
  int _mealPlanDuration = 5; // Default to 5 days if not provided
  dynamic viewDayData; // Store view day response for edit mode
  int? _selectedChefId; // Store selected chef ID for edit mode

  @override
  void initState() {
    super.initState();
    _mealPlanBloc = BlocProvider.of<MealplanBloc>(context);

    if (widget.isEditing && widget.dayId != null) {
      // In edit mode, first get the day data
      _mealPlanBloc.add(ViewDayEvent(widget.dayId!));
    } else {
      // In normal mode, get meal plan progress
      _mealPlanBloc.add(MealPlanProgressEvent(widget.mealPlanId));
    }

    _selectedDate = 1;
    _currentDay = 1;
  }

  late double ten;
  late double twelve;
  late double forteen;
  late double sixteen;
  late double eighteen;
  late double twenty;
  late double twentyFour;
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final screenWidth = MediaQuery.of(context).size.width;

    ten = screenWidth * 0.02545;
    twelve = screenWidth * 0.03054;
    forteen = screenWidth * 0.035;
    sixteen = screenWidth * 0.04073;
    eighteen = screenWidth * 0.04582;
    twenty = screenWidth * 0.05091;
    twentyFour = screenWidth * 0.06109;
  }

  String _formatDate(String date) {
    if (date.isEmpty) return '';
    try {
      final DateTime dateTime = DateTime.parse(date);
      final months = [
        'Jan',
        'Feb',
        'Mar',
        'Apr',
        'May',
        'Jun',
        'Jul',
        'Aug',
        'Sep',
        'Oct',
        'Nov',
        'Dec'
      ];
      final days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
      return "${months[dateTime.month - 1]} ${dateTime.day}, ${dateTime.year}, ${days[dateTime.weekday - 1]}";
    } catch (e) {
      return date;
    }
  }

  void _updateStartDateBasedOnSelected() {
    if (_nextDatesFromStartDate.isEmpty || _selectedDate == 0) return;
    int index = _selectedDate - 1;
    if (index >= 0 && index < _nextDatesFromStartDate.length) {
      setState(() {
        _startDate = _nextDatesFromStartDate[index];
      });
    }
  }

  void _selectChef(Map<String, dynamic> chef) {
    if (_currentDay == 0) return;
    setState(() {
      chef['id'] = chef['chef_id'] ?? 0;
      _selectedChefs[_currentDay] = chef;
      if (_selectedChefs.length < _mealPlanDuration) {
        _currentDay++;
        _selectedDate = _currentDay;
        if (_nextDatesFromStartDate.isNotEmpty && _selectedDate > 1) {
          _startDate = _nextDatesFromStartDate[_selectedDate - 1];
        }
      }
      if (_selectedChefs.length == _mealPlanDuration) {
        List<Map<String, dynamic>> selectedChefsWithDates = [];
        for (int day = 1; day <= _mealPlanDuration; day++) {
          if (_selectedChefs.containsKey(day)) {
            final chefData = _selectedChefs[day]!;
            selectedChefsWithDates.add({
              'date': chefData['date'] ?? _nextDatesFromStartDate[day - 1],
              'chef_id': chefData['chef_id'] ?? 0,
              'name': chefData['name'] ?? 'Unknown Chef',
              'image': chefData['image'] ?? '',
              'tags': chefData['tags'] ?? [],
            });
          }
        }
        if (mounted) {
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(
              builder: (context) => MealPlan(
                selectedChefsWithDetails: selectedChefsWithDates,
                mealPlanId: widget.mealPlanId,
              ),
            ),
          );
        }
      }
    });
  }

  bool _isDayCompleted(int dayNumber) {
    return _selectedChefs.containsKey(dayNumber);
  }

  List<String> _generateNextDates(String startDate) {
    if (startDate.isEmpty) return [];
    List<String> dates = [];
    DateTime currentDate;
    try {
      currentDate = DateTime.parse(startDate);
    } catch (e) {
      currentDate = DateTime.now();
    }
    for (int i = 0; i < _mealPlanDuration; i++) {
      if (i == 0) {
        dates.add(currentDate.toString().split(' ')[0]);
        continue;
      }
      do {
        currentDate = currentDate.add(const Duration(days: 1));
      } while (currentDate.weekday == DateTime.saturday ||
          currentDate.weekday == DateTime.sunday);
      dates.add(currentDate.toString().split(' ')[0]);
    }
    return dates;
  }

  void _loadFilteredChefs(progress.Data? progressData) {
    if (progressData == null) return;
    final filterData = {
      "search_keyword": "",
      "latitude": Initializer.latitude ?? "0",
      "longitude": Initializer.longitude ?? "0",
      "serving_size_id": progressData.servingSizeId,
      "time_slot_id": progressData.timeSlotId,
      "date": _nextDatesFromStartDate.isNotEmpty
          ? _nextDatesFromStartDate[_currentDay - 1]
          : (progressData.startDate ?? DateTime.now().toString().split(' ')[0]),
      "cuisine_ids": progressData.cuisines?.map((c) => c.id).toList() ?? [],
      "sub_cuisine_ids":
          progressData.subcuisines?.map((c) => c.id).toList() ?? [],
      "local_cuisine_ids":
          progressData.localcuisines?.map((c) => c.id).toList() ?? [],
      "dietary_ids": progressData.dietaryPreferenceId != null
          ? [progressData.dietaryPreferenceId]
          : [],
      "spice_level_ids":
          progressData.spiceLevelId != null ? [progressData.spiceLevelId] : []
    };
    _mealPlanBloc.add(FilterChefsEvent(filterData));
  }

  String _formatTimeToAmPm(String? time) {
    if (time == null) return '';
    try {
      final timeParts = time.split(':');
      int hour = int.parse(timeParts[0]);
      int minute = int.parse(timeParts[1]);
      String period = hour >= 12 ? 'PM' : 'AM';
      hour = hour > 12 ? hour - 12 : hour;
      hour = hour == 0 ? 12 : hour;
      return '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}$period';
    } catch (e) {
      return time;
    }
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isTablet = size.width >= 600;
    final isLandscape =
        MediaQuery.of(context).orientation == Orientation.landscape;

    return MultiBlocListener(
      listeners: [
        BlocListener<MealplanBloc, MealPlanState>(
          listener: (context, state) {
            if (state is ViewDaySuccess) {
              // Handle edit mode - get day data first
              print('ViewDaySuccess received: ${state.data}');
              final dayData = state.data;
              // Fix: chef data is nested, get the id from chef object
              final chefId = dayData['chef']?['id'];
              final dayDate = dayData['date'];

              print('Chef ID from day data: $chefId');
              print('Date from day data: $dayDate');

              setState(() {
                _selectedChefId = chefId;
                _startDate = widget.date ?? dayDate ?? '';
                viewDayData = state.data; // Store view day response
                _currentDay = 1; // Set to day 1 for edit mode
                _selectedDate = 1;
              });

              // Now get meal plan progress to load the filters and chef data
              _mealPlanBloc.add(MealPlanProgressEvent(widget.mealPlanId));
            } else if (state is ViewDayFailed) {
              print('ViewDayFailed: ${state.message}');
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Failed to load day data: ${state.message}'),
                  backgroundColor: const Color(0xFFE11900),
                ),
              );
              // Navigate back if day data fails to load
              Navigator.of(context).pop();
            } else if (state is MealPlanProgressSuccess) {
              final progressData = state.data
                  as progress.Data; // Use Data from MealPlanProgressModel
              final List<String> nextDates =
                  _generateNextDates(progressData.startDate ?? '');
              setState(() {
                _isLoading = false;
                if (!widget.isEditing) {
                  _startDate = progressData.startDate ?? '';
                }
                _mealPlanDuration =
                    progressData.mealPlanDuration ?? 5; // Default to 5 if null
                _nextDatesFromStartDate = nextDates;
                if (progressData.timeSlot != null) {
                  _deliveryTime =
                      '${_formatTimeToAmPm(progressData.timeSlot?.startTime)}-${_formatTimeToAmPm(progressData.timeSlot?.endTime)}';
                }
              });
              _loadFilteredChefs(progressData);
            } else if (state is FilterChefsSuccess) {
              final filterData = state.data as filter.Data;
              setState(() {
                _chefs = filterData.chefs ?? [];
              });
            }
          },
        ),
      ],
      child: BlocBuilder<MealplanBloc, MealPlanState>(
        builder: (context, state) {
          if (state is MealPlanProgressLoading) {
            return SafeArea(
              child: Scaffold(
                backgroundColor: const Color(0xFFF6F3EC),
                appBar: _buildAppBar(size),
                body: _buildFullScreenShimmer(size),
              ),
            );
          }

          final isLoading = state is FilterChefsLoading;
          return SafeArea(
            child: Scaffold(
              backgroundColor: const Color(0xFFF6F3EC),
              appBar: _buildAppBar(size),
              body: ListView(
                padding: EdgeInsets.symmetric(vertical: size.height * 0.02),
                children: [
                  // Title
                  Padding(
                    padding:
                        EdgeInsets.symmetric(horizontal: size.width * 0.04),
                    child: Text(
                      "Select Chef",
                      style: TextStyle(
                        fontSize: isTablet ? size.width * 0.05 : eighteen,
                        fontWeight: FontWeight.w600,
                        fontFamily: 'Inter',
                        color: const Color(0xFF1F2222),
                      ),
                    ),
                  ),
                  SizedBox(height: size.height * 0.015),
                  // Date and Time information
                  Padding(
                    padding:
                        EdgeInsets.symmetric(horizontal: size.width * 0.04),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
  // Main row with date/time column and date selector
  Row(
    crossAxisAlignment: CrossAxisAlignment.center,
    children: [
      // Left column - Date and delivery time
      Expanded(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Date row
            Row(
              children: [
                Image.asset(
                  'assets/icons/date_range.png',
                  width: twelve,
                  height: twelve,
                  color: const Color(0xFF1F2222),
                ),
                SizedBox(width: size.width * 0.02),
                Text(
                  _isLoading
                      ? "Loading..."
                      : widget.isEditing
                          ? _formatDate(widget.date ?? _startDate)
                          : (_currentDay > 0 &&
                                  _nextDatesFromStartDate
                                      .isNotEmpty)
                              ? _formatDate(_nextDatesFromStartDate[
                                  _currentDay - 1])
                              : _formatDate(_startDate),
                  style: TextStyle(
                    fontSize: twelve,
                    fontWeight: FontWeight.w500,
                    fontFamily: 'Inter',
                    color: const Color(0xFF414346),
                  ),
                ),
              ],
            ),
            SizedBox(height: size.height * 0.005),
            // Delivery time row
            Row(
              children: [
                Icon(Icons.access_time,
                    size: twelve, color: const Color(0xFF1F2222)),
                SizedBox(width: size.width * 0.02),
                Text(
                  isLoading ? "Loading..." : _deliveryTime,
                  style: TextStyle(
                    fontSize: twelve,
                    fontWeight: FontWeight.w500,
                    fontFamily: 'Inter',
                    color: const Color(0xFF414346),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
      // Right column - Date selector (centrally aligned)
      _buildDateSelector(size, isLandscape),
    ],
  ),
],
                    ),
                  ),
                  SizedBox(height: sixteen / 2),
                  // View Filters button
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: sixteen),
                    child: Container(
                      height: twentyFour + twentyFour,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(size.width * 0.07),
                        border: Border.all(color: const Color(0xFF1F2222)),
                      ),
                      child: InkWell(
                        borderRadius: BorderRadius.circular(size.width * 0.07),
                        onTap: () {
                          // Handle filter action
                        },
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.tune,
                                size: size.width * 0.04,
                                color: const Color(0xFF1F2222)),
                            SizedBox(width: size.width * 0.02),
                            Text(
                              "View Filters",
                              style: TextStyle(
                                fontSize: twelve,
                                fontWeight: FontWeight.w600,
                                fontFamily: 'Inter',
                                color: const Color(0xFF1F2222),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  SizedBox(height: size.height * 0.02),
                  // Chef Cards
                  isLoading
                      ? _buildChefShimmerGrid(size, isTablet, isLandscape)
                      : _chefs.isEmpty
                          ? _buildNoChefMessage(size)
                          : isTablet
                              ? _buildChefGrid(size, isLandscape)
                              : _buildChefList(size),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(Size size) {
    return AppBar(
      backgroundColor: const Color(0xFFF6F3EC),
      elevation: 0,
      scrolledUnderElevation: 0,
      foregroundColor: const Color(0xFF1F2222),
      surfaceTintColor: Colors.transparent,
      leading: IconButton(
        icon: Icon(Icons.arrow_back,
            size: size.width * 0.06, color: const Color(0xFF1F2222)),
        onPressed: () => Navigator.of(context).pop(),
      ),
      title: const Text(''),
      centerTitle: false,
    );
  }

  Widget _buildDateSelector(Size size, bool isLandscape) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final circleSize = (size.width * 0.06).clamp(20.0, 25.0);
        final connectorWidth = (size.width * 0.02).clamp(5.0, 7.0);
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: List.generate(_mealPlanDuration, (index) {
            final number = index + 1;

            // In edit mode, highlight the day that corresponds to the selected date
            bool isSelected = false;
            if (widget.isEditing) {
              // Find which day corresponds to the selected date
              if (!_isLoading && _startDate.isNotEmpty) {
                final dates = _generateNextDates(_startDate);
                if (index < dates.length) {
                  final dayDate = dates[index];
                  final selectedDateOnly =
                      (widget.date ?? '').split(',')[0].trim();
                  isSelected = dayDate == selectedDateOnly;
                }
              }
            } else {
              isSelected = _selectedDate == number;
            }

            final isCompleted = _isDayCompleted(number);
            final isFirst = index == 0;
            final isLast = index == _mealPlanDuration - 1;
            String displayDate = '';
            if (!_isLoading && _startDate.isNotEmpty) {
              final dates = _generateNextDates(_startDate);
              if (index < dates.length) {
                final date = DateTime.parse(dates[index]);
                displayDate = '${date.day}/${date.month}';
              }
            }
            return Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (!isFirst)
                  Container(
                    width: connectorWidth,
                    height: 1,
                    color: const Color(0xFFB9B6AD),
                  ),
                Tooltip(
                  message: displayDate,
                  child: InkWell(
                    onTap: () {
                      setState(() {
                        _selectedDate = number;
                        _currentDay = number;
                        _updateStartDateBasedOnSelected();
                      });
                    },
                    borderRadius: BorderRadius.circular(circleSize / 2),
                    child: Container(
                      width: circleSize,
                      height: circleSize,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: (isSelected || isCompleted)
                            ? const Color(0xFF1F2222)
                            : Colors.transparent,
                        border: Border.all(
                          color: (isSelected || isCompleted)
                              ? const Color(0xFF1F2222)
                              : const Color(0xFFB9B6AD),
                          width: 1,
                        ),
                      ),
                      child: Center(
                        child: Text(
                          "$number",
                          style: TextStyle(
                            fontSize: circleSize * 0.5,
                            fontWeight: FontWeight.w600,
                            fontFamily: 'Inter',
                            color: (isSelected || isCompleted)
                                ? Colors.white
                                : const Color(0xFF1F2222),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                if (!isLast)
                  Container(
                    width: connectorWidth,
                    height: 1,
                    color: const Color(0xFFB9B6AD),
                  ),
              ],
            );
          }),
        );
      },
    );
  }

  Widget _buildChefList(Size size) {
    return Column(
      children: _chefs.map((chef) => _buildChefCard(chef, size)).toList(),
    );
  }

  Widget _buildChefGrid(Size size, bool isLandscape) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: EdgeInsets.symmetric(horizontal: size.width * 0.04),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: isLandscape ? 3 : 2,
        crossAxisSpacing: size.width * 0.02,
        mainAxisSpacing: size.height * 0.02,
        childAspectRatio: 0.75,
      ),
      itemCount: _chefs.length,
      itemBuilder: (context, index) {
        return _buildChefCard(_chefs[index], size);
      },
    );
  }

  Widget _buildChefCard(Chefs chef, Size size) {
    return Container(
        margin: EdgeInsets.fromLTRB(
          sixteen,
          0,
          sixteen,
          sixteen / 2,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(size.width * 0.04),
          border: Border.all(
            color: _selectedChefs.containsValue(chef)
                ? const Color(0xFF1F2222)
                : Colors.transparent,
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: InkWell(
          onTap: () => _selectChef({
            'chef_id': chef.chefId ?? 0,
            'name': '${chef.chef?.firstName} ${chef.chef?.lastName}',
            'image': chef.profilePhoto ?? '',
            'rating': '82%',
            'reviews': '(49)',
            'tags': chef.searchTags ?? [],
            'availability': chef.chef?.operationDays
                    ?.map((day) => day.day?.name?.substring(0, 1) ?? '')
                    .toList() ??
                [],
            'dishes': [],
            'date': _nextDatesFromStartDate[_currentDay - 1],
          }),
          child: Padding(
            padding: EdgeInsets.all(size.width * 0.04),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    CircleAvatar(
                      radius: twelve,
                      backgroundColor: const Color(0xFFE1DDD5),
                      backgroundImage: chef.profilePhoto != null &&
                              chef.profilePhoto!.isNotEmpty
                          ? NetworkImage(
                              ServerHelper.imageUrl + chef.profilePhoto!)
                          : const AssetImage(
                                  'assets/images/no_image_avatar.png')
                              as ImageProvider,
                      onBackgroundImageError: (e, stackTrace) {
                        setState(() {
                          chef.profilePhoto = null;
                        });
                      },
                    ),
                    SizedBox(
                      width: twelve,
                    ),
                    Expanded(
                      child: Text(
                        '${chef.chef?.firstName} ${chef.chef?.lastName}',
                        style: TextStyle(
                          fontSize: sixteen,
                          fontWeight: FontWeight.w600,
                          fontFamily: 'Inter',
                          color: const Color(0xFF1F2222),
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(
                  height: sixteen / 2,
                ),
                Container(
                  padding: EdgeInsets.symmetric(
                      horizontal: sixteen / 2, 
                      //vertical: sixteen / 4
                      ),
                  decoration: BoxDecoration(
                    color: const Color(0xFFE1E3E6),
                    borderRadius: BorderRadius.circular(size.width * 0.03),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Image.asset(
                        'assets/icons/thump.png',
                        width: twenty / 2,
                        height: twenty / 2,
                        color: const Color(0xFF1F2222),
                      ),
                      SizedBox(width: sixteen / 4),
                      Text(
                        "82% (49)",
                        style: TextStyle(
                          fontSize: twenty / 2,
                          fontWeight: FontWeight.w500,
                          fontFamily: 'Inter',
                          color: const Color(0xFF1F2222),
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: size.height * 0.01),
                Text(
                  chef.searchTags?.join(", ") ?? '',
                  style: TextStyle(
                    fontSize: twelve,
                    fontWeight: FontWeight.w400,
                    fontFamily: 'Inter',
                    color: const Color(0xFF414346),
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: sixteen / 4),
                Row(
                  children: [
                    Image.asset(
                      'assets/icons/calender_2.png',
                      width: twenty / 2,
                      height: twenty / 2,
                      color: const Color(0xFF1F2222),
                    ),
                    SizedBox(width: ten / 5),
                    Text(
                      chef.chef?.operationDays
                              ?.map(
                                  (day) => day.day?.name?.substring(0, 1) ?? '')
                              .join(", ") ??
                          '',
                      style: TextStyle(
                        fontSize: twelve,
                        fontWeight: FontWeight.w400,
                        fontFamily: 'Inter',
                        color: const Color(0xFF1F2222),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: sixteen / 2),
                OutlinedButton(
                  onPressed: () async {
                    if (_currentDay > 0 || widget.isEditing) {
                      final result = await Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => ViewChefmenu(
                            mealPlanId: widget.mealPlanId,
                            id: chef.chefId ?? 0,
                            selectedDay: widget.isEditing ? 1 : _currentDay,
                            deliveryTime: _deliveryTime,
                            selectedDate: widget.isEditing
                                ? (widget.date ?? _startDate)
                                : _nextDatesFromStartDate[_currentDay - 1],
                            mealPlanDuration: _mealPlanDuration,
                            isEditing: widget
                                .isEditing, // Always pass edit mode if we're editing
                            dayId: widget.isEditing ? widget.dayId : null,
                            viewDayData: widget.isEditing ? viewDayData : null,
                            chefLocation: chef.distance != null
                                ? '${chef.distance!.toStringAsFixed(1)} km'
                                : null, // Pass chef distance as location
                          ),
                          settings: RouteSettings(
                              arguments: _selectedChefs.values.toList()),
                        ),
                      );

                      if (widget.isEditing && result != null) {
                        // In edit mode, navigate back to checkout
                        if (mounted) {
                          Navigator.pushReplacement(
                            context,
                            MaterialPageRoute(
                              builder: (context) => CheckoutPage(
                                mealPlanId: widget.mealPlanId,
                              ),
                            ),
                          );
                        }
                      } else if (result != null) {
                        setState(() {
                          _selectedChefs[_currentDay] = result;
                          if (_selectedChefs.length < _mealPlanDuration) {
                            _currentDay++;
                            _selectedDate = _currentDay;
                          }
                        });
                      }
                    } else {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Please select a day first'),
                          backgroundColor: Color(0xFFE11900),
                        ),
                      );
                    }
                  },
                  style: OutlinedButton.styleFrom(
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(size.width * 0.07)),
                    side: const BorderSide(color: Color(0xFF1F2222)),
                    foregroundColor: const Color(0xFF1F2222), minimumSize: Size(0, 0),
                    padding: EdgeInsets.symmetric(
                        vertical: size.height * 0.008,
                        horizontal: size.width * 0.04),
                  ),
                  child: Text(
                    'View Chef Menu',
                    style: TextStyle(
                      fontSize: twelve,
                      fontWeight: FontWeight.w400,
                      fontFamily: 'Inter',
                      color: const Color(0xFF1F2222),
                    ),
                  ),
                ),
                SizedBox(height: sixteen / 2),
                SizedBox(
                  height: ten * 12 + sixteen,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: chef.chef?.dishes?.length ?? 0,
                    itemBuilder: (context, index) {
                      final dish = chef.chef?.dishes?[index];
                      return Container(
                        width: ten * 15 + eighteen,
                        margin: EdgeInsets.only(
                            right: index == (chef.chef?.dishes?.length ?? 0) - 1
                                ? 0
                                : sixteen / 2),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius:
                              BorderRadius.circular(size.width * 0.02),
                          border: Border.all(color: const Color(0xFFE1DDD5)),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            ClipRRect(
                              borderRadius: BorderRadius.vertical(
                                  top: Radius.circular(size.width * 0.02)),
                              child: dish?.photo != null
                                  ? Image.network(
                                      '${ServerHelper.imageUrl}${dish?.photo}',
                                      height: ten * 8 + twelve,
                                      width: double.infinity,
                                      fit: BoxFit.cover,
                                      errorBuilder:
                                          (context, error, stackTrace) {
                                        return Container(
                                          height: ten * 12,
                                          color: const Color(0xFFE1DDD5),
                                          child: const Center(
                                            child: Icon(Icons.restaurant_menu,
                                                color: Color(0xFF1F2222)),
                                          ),
                                        );
                                      },
                                    )
                                  : Container(
                                      height: ten * 12,
                                      color: const Color(0xFFE1DDD5),
                                      child: const Center(
                                        child: Icon(Icons.restaurant_menu,
                                            color: Color(0xFF1F2222)),
                                      ),
                                    ),
                            ),
                            Padding(
                              padding: EdgeInsets.all(twelve),
                              child: Text(
                                dish?.name ?? '',
                                style: TextStyle(
                                  fontSize: twelve,
                                  fontWeight: FontWeight.w600,
                                  color: const Color(0xFF1F2222),
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ));
  }

  Widget _buildNoChefMessage(Size size) {
    return Container(
      margin: EdgeInsets.fromLTRB(size.width * 0.04, size.height * 0.04,
          size.width * 0.04, size.height * 0.02),
      padding: EdgeInsets.all(size.width * 0.06),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(size.width * 0.04),
        border: Border.all(color: const Color(0xFFE1DDD5)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Image.asset(
            'assets/images/cooking.png',
            width: size.width * 0.2,
            height: size.width * 0.2,
          ),
          SizedBox(height: size.height * 0.02),
          Text(
            'No Chefs Available',
            style: TextStyle(
              fontSize: size.width * 0.045,
              fontWeight: FontWeight.w600,
              fontFamily: 'Inter',
              color: const Color(0xFF1F2222),
            ),
          ),
          SizedBox(height: size.height * 0.01),
          Text(
            'Sorry, there are no chefs available in your area at the moment.',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: size.width * 0.035,
              fontWeight: FontWeight.w400,
              fontFamily: 'Inter',
              color: const Color(0xFF414346),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFullScreenShimmer(Size size) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: ListView(
        padding: EdgeInsets.symmetric(vertical: size.height * 0.02),
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: size.width * 0.04),
            child: Container(
              height: size.height * 0.04,
              width: size.width * 0.4,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ),
          SizedBox(height: size.height * 0.015),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: size.width * 0.04),
            child: Column(
              children: [
                Row(
                  children: [
                    Container(
                      width: size.width * 0.04,
                      height: size.width * 0.04,
                      color: Colors.white,
                    ),
                    SizedBox(width: size.width * 0.02),
                    Container(
                      width: size.width * 0.4,
                      height: size.height * 0.02,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                    const Spacer(),
                    Row(
                      children: List.generate(
                        _mealPlanDuration,
                        (index) => Padding(
                          padding: EdgeInsets.symmetric(
                              horizontal: size.width * 0.01),
                          child: Container(
                            width: size.width * 0.06,
                            height: size.width * 0.06,
                            decoration: const BoxDecoration(
                              color: Colors.white,
                              shape: BoxShape.circle,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: size.height * 0.01),
                Row(
                  children: [
                    Container(
                      width: size.width * 0.04,
                      height: size.width * 0.04,
                      color: Colors.white,
                    ),
                    SizedBox(width: size.width * 0.02),
                    Container(
                      width: size.width * 0.3,
                      height: size.height * 0.02,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          SizedBox(height: size.height * 0.02),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: size.width * 0.04),
            child: Container(
              height: size.height * 0.06,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(size.width * 0.07),
              ),
            ),
          ),
          SizedBox(height: size.height * 0.02),
          _buildChefShimmerGrid(size, false, false),
        ],
      ),
    );
  }

  Widget _buildChefShimmerGrid(Size size, bool isTablet, bool isLandscape) {
    if (isTablet) {
      return GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        padding: EdgeInsets.symmetric(horizontal: size.width * 0.04),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: isLandscape ? 3 : 2,
          crossAxisSpacing: size.width * 0.02,
          mainAxisSpacing: size.height * 0.02,
          childAspectRatio: 0.75,
        ),
        itemCount: 3,
        itemBuilder: (context, index) => _buildChefShimmerCard(size),
      );
    }
    return Column(
      children: List.generate(
        3,
        (index) => Padding(
          padding: EdgeInsets.fromLTRB(
              size.width * 0.04, 0, size.width * 0.04, size.height * 0.02),
          child: _buildChefShimmerCard(size),
        ),
      ),
    );
  }

  Widget _buildChefShimmerCard(Size size) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(size.width * 0.04),
        ),
        child: Padding(
          padding: EdgeInsets.all(size.width * 0.04),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    width: size.width * 0.12,
                    height: size.width * 0.12,
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                    ),
                  ),
                  SizedBox(width: size.width * 0.03),
                  Container(
                    width: size.width * 0.4,
                    height: size.height * 0.025,
                    color: Colors.white,
                  ),
                ],
              ),
              SizedBox(height: size.height * 0.015),
              Container(
                width: size.width * 0.2,
                height: size.height * 0.03,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(size.width * 0.03),
                ),
              ),
              SizedBox(height: size.height * 0.015),
              Container(
                width: size.width * 0.6,
                height: size.height * 0.02,
                color: Colors.white,
              ),
              SizedBox(height: size.height * 0.02),
              Container(
                width: size.width * 0.3,
                height: size.height * 0.02,
                color: Colors.white,
              ),
              SizedBox(height: size.height * 0.015),
              Container(
                width: size.width * 0.4,
                height: size.height * 0.05,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(size.width * 0.07),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
